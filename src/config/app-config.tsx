/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import config from '../config';
import CTLightLogo from '../images/apps/Company_Tracker_logo.svg';
import ESGenomeLogo from '../images/SGX-ESGenome.svg';
import ESGenomeBlueLogo from '../images/SGX-ESGenome-Blue.svg';
import SGXFirstLogo from '../images/sgx-first-logo.png';
import { SurveyOverviewMode } from '../slice/surveySettings';
import { CompanyAgreement, UserAgreement } from '../reducers/current-user';
import { isCTL } from '../utils/permission-groups';
import { AppConfig } from '../types/app';

export enum AppFlavour {
  DEFAULT = 'prod',
  SINGAPORE = 'singapore'
}

const defaultAddons: string[] = [
  'ctl',
  'sgx_metrics',
  'sgx_extended',
  'gri',
  'gri2021',
  'tcfd',
  'ungc',
  'cdsb',
  'wef',
  'sasb',
  'cdp_2022',
  // 'sam_csa',
  // 'vigeo_eiris',
  // 'bof',
  'rspo',
  'iogp',
  'ipieca',
  // 'refinitiv',
];

type AppConfigSettings = AppConfig['settings'];
interface BrandingConfigSettings extends AppConfigSettings {
  supportedPaths: string[];
  whitelabel?: {
    logo?: string;
  };
  rootAppPath: string;
  logo: string;
  homeUrl?: string;
  cardLogo: string;
  name: string;
  subtitle: string;
  description: JSX.Element;
  canRegister: boolean;
  defaultSurveyOverviewMode: SurveyOverviewMode;
  sponsoredByLogo?: string;
  productSlug?: string;
}

export interface BrandingConfig {
  flavour: AppFlavour;
  branding: {
    ctlApp: BrandingConfigSettings;
  },
  locationWhitelist?: string[]; //ISO_3166-1_alpha-2
}

const supportedPaths = ['company-tracker', 'sgx-esgenome', 'wwg'];
const configuration: { [key in AppFlavour]: BrandingConfig } = {
  [AppFlavour.DEFAULT]: {
    flavour: AppFlavour.DEFAULT,
    branding: {
      ctlApp: {
        supportedPaths,
        rootAppPath: 'company-tracker',
        logo: CTLightLogo,
        cardLogo: CTLightLogo,
        name: 'Company Tracker',
        subtitle: 'Start your sustainability journey with a quick business healthcheck',
        description: <>Aimed at companies that want to start their sustainability journey and get a quick healthcheck
          of their business. It will help you understand where your risks and vulnerabilities are, but also where you
          are doing well! From there we can help you embed sustainability into the DNA of your business, set targets
          and build a strategy to peak performance.</>,
        defaultSurveyOverviewMode: SurveyOverviewMode.ScopeGroups,
        overviewRecommendedAddons: ['ctl'],
        settingsRecommendedAddons: ['ctl'],
        canRegister: true,
        companyAgreementsRequired: [
          {
            code: CompanyAgreement.CompanyTrackerServicesAgreement,
            fromDate: new Date('2023-04-25T00:00:00.000Z'),
          }
        ],
        productSlug: 'company-tracker',
        availableAddons: defaultAddons,
      }
    }
  },
  [AppFlavour.SINGAPORE]: {
    flavour: AppFlavour.SINGAPORE,
    branding: {
      ctlApp: {
        supportedPaths,
        whitelabel: {
          logo: SGXFirstLogo
        },
        rootAppPath: 'sgx-esgenome',
        logo: ESGenomeLogo,
        homeUrl: '/sgx-esgenome',
        cardLogo: ESGenomeBlueLogo,
        name: 'SGX ESGenome',
        subtitle: 'An initiative by Singapore Exchange to support issuers in their ESG disclosure journey.',
        description: <>An initiative by Singapore Exchange to support issuers in their ESG disclosure journey.
          For more information, contact <a href='mailto:<EMAIL>' rel='noreferrer' ><EMAIL></a>.</>,
        defaultSurveyOverviewMode: SurveyOverviewMode.ScopeGroups,
        overviewRecommendedAddons: ['sgx_metrics', 'sgx_extended', 'tcfd'],
        settingsRecommendedAddons: ['sgx_metrics', 'sgx_extended', 'tcfd'],
        canRegister: true,
        requiresTermsAndConditions: false,
        sponsoredByLogo: SGXFirstLogo,
        userAgreementsRequired: [
          {
            code: UserAgreement.ESGenomeTandC
          }
        ],
        productSlug: 'sgx-esgenome',
        availableAddons: [
          'sgx_metrics',
          'sgx_extended',
          ...defaultAddons,
        ],
      }
    },
    locationWhitelist: ['SG']
  }
}

const appEnv = config.appEnv as AppFlavour;
const appBranding = config.appBrandingEnv as AppFlavour;
const appConfig = configuration[appBranding] ?? configuration[appEnv] ?? configuration[AppFlavour.DEFAULT];

export const getDefaultConfig = () => appConfig;
export const getBrandingConfig = (flavour: AppFlavour) => configuration[flavour]

export enum MainDownloadCode {
  SGX_Metrics = 'sgx_metrics',
  CTL = 'ctl',
}
export const getMainDownloadCode = (appConfigCode?: string, permissionGroup?: string): MainDownloadCode => {
  if (appConfigCode === 'sgx_esgenome') {
    return MainDownloadCode.SGX_Metrics;
  }

  if (appConfigCode === 'company_tracker_light') {
    return MainDownloadCode.CTL;
  }
  // company tracker pro on singapore env so it does not default to sgx_metrics
  if (!isCTL({permissionGroup})) {
    return MainDownloadCode.CTL;
  }
  return appConfig.flavour === AppFlavour.SINGAPORE ? MainDownloadCode.SGX_Metrics : MainDownloadCode.CTL
}
