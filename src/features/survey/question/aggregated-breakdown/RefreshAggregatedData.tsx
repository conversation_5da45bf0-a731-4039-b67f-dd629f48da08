import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON> } from 'reactstrap';
import { useState } from 'react';
import UniversalTracker from '@models/UniversalTracker';
import { SurveyModelMinimalUtrv, UnitConfig } from '@models/surveyData';
import G17Client from '@services/G17Client';
import { toastFromError } from '@components/toasts';
import { FeatureStability } from '../../../../molecules/feature-stability';
import { DataDiffView } from './DataDiffView';
import { PreviewData } from './types';

interface Props {
  utr: UniversalTracker;
  utrv: SurveyModelMinimalUtrv;
  unitConfig?: UnitConfig;
  onRefresh: (() => void) | undefined;
}

const getRefreshUrl = (utrvId: string) => `/universal-tracker-values/${utrvId}/aggregate/refresh`;

export const RefreshAggregatedData = ({ utr, utrv, unitConfig, onRefresh }: Props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [previewData, setPreviewData] = useState<PreviewData | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handlePreview = async () => {
    setIsLoading(true);
    try {
      const response = await G17Client.post(getRefreshUrl(utrv._id), { action: 'preview' });
      setPreviewData(response.data.data);
      setIsModalOpen(true);
    } catch (error) {
      toastFromError({ error, title: 'Failed to preview refresh' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdate = async () => {
    setIsLoading(true);
    try {
      await G17Client.post(getRefreshUrl(utrv._id), { action: 'update' });
      setIsModalOpen(false);
      onRefresh?.();
    } catch (error) {
      toastFromError({ error, title: 'Failed to update aggregated data' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Button color='secondary' onClick={handlePreview} disabled={isLoading} className='mb-3'>
        {isLoading ? 'Loading...' : 'Preview Aggregated Data'}
        <FeatureStability stability={'internal'} />
      </Button>

      <Modal key={utrv._id} isOpen={isModalOpen} toggle={() => setIsModalOpen(false)} size='lg'>
        <ModalHeader toggle={() => setIsModalOpen(false)}>Preview Aggregated Data Refresh</ModalHeader>
        <ModalBody>
          {previewData && (
            <DataDiffView
              utr={utr}
              current={previewData.current}
              next={previewData.next}
              unitConfig={unitConfig}
            />
          )}
        </ModalBody>
        <ModalFooter>
          <Button color='secondary' onClick={() => setIsModalOpen(false)}>
            Cancel
          </Button>
          <Button color='primary' onClick={handleUpdate} disabled={isLoading}>
            {isLoading ? 'Updating...' : 'Apply Changes'}
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
};
