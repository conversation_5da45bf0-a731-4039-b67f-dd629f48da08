import UniversalTracker from '@models/UniversalTracker';
import { UtrValueType } from '@g17eco/types/universalTracker';
import { tableDataToView } from '@utils/valueDataTable';
import TableInputView from '@components/survey/form/view/TableInputView';
import { renderNumericValue, conversionParams } from '@utils/universalTrackerValue';
import { getValueListData } from '@utils/universalTracker';
import { useUtrValueList } from '@hooks/useUtrValueList';
import { DATE, formatDateUTC } from '@utils/date';
import { PreviewData, PreviewUtrv } from './types';
import { useAppSelector } from '@reducers/index';
import { isStaff } from '@selectors/user';
import { useState } from 'react';
import { ValueAggregation, valueAggregationMap } from '../../../../types/disaggregation';
import { FeatureStability } from '../../../../molecules/feature-stability';
import { UnitConfig } from '@models/surveyData';

interface Props extends PreviewData {
  utr: UniversalTracker;
  unitConfig?: UnitConfig;
}

export const DataDiffView = ({ utr, current, next, unitConfig }: Props) => {
  const options = useUtrValueList(utr);
  const valueType = utr.getValueType();
  const isUserStaff = useAppSelector(isStaff);
  const [showJson, setShowJson] = useState(false);

  const renderValue = (data: PreviewUtrv) => {
    switch (valueType) {
      case UtrValueType.Table: {
        if (!data.valueData?.table?.length) {
          return null;
        }
        const { rows, editRowId } = tableDataToView(data.valueData);
        return (
          <TableInputView
            universalTracker={utr}
            disabled={true}
            editRowId={editRowId}
            rowData={rows}
            showTableDownload={true}
            unitConfig={unitConfig}
          />
        );
      }

      case UtrValueType.NumericValueList: {
        if (!data.valueData?.data) {
          return null;
        }
        const dataObj = data.valueData.data as Record<string, number>;
        return (
          <div className='table-responsive'>
            <table className='table table-sm'>
              <thead>
                <tr>
                  <th>Category</th>
                  <th>Value</th>
                </tr>
              </thead>
              <tbody>
                {Object.entries(dataObj).map(([key, value]) => (
                  <tr key={key}>
                    <td>{getValueListData(key, options)}</td>
                    <td>
                      {renderNumericValue(
                        conversionParams({ valueData: data.valueData }, utr, {
                          value: data.value,
                          unit: data.unit,
                          numberScale: data.numberScale,
                        }),
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        );
      }

      case UtrValueType.ValueList:
      case UtrValueType.ValueListMulti: {
        if (!data.valueData?.data) {
          return null;
        }
        const values = Array.isArray(data.valueData.data) ? data.valueData.data : [data.valueData.data];
        return (
          <div className='table-responsive'>
            <table className='table table-sm'>
              <tbody>
                {values.map((value, index) => (
                  <tr key={index}>
                    <td>{getValueListData(value, options)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        );
      }

      case UtrValueType.TextValueList: {
        if (!data.valueData?.data) {
          return null;
        }
        const dataObj = data.valueData.data as Record<string, string>;
        return (
          <div className='table-responsive'>
            <table className='table table-sm'>
              <thead>
                <tr>
                  <th>Category</th>
                  <th>Value</th>
                </tr>
              </thead>
              <tbody>
                {Object.entries(dataObj).map(([key, value]) => (
                  <tr key={key}>
                    <td>{getValueListData(key, options)}</td>
                    <td>{value}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        );
      }

      case UtrValueType.Text: {
        return <div className='p-2 border rounded'>{data.valueData?.data}</div>;
      }

      case UtrValueType.Date: {
        return <div className='p-2 border rounded'>{data.valueData?.data}</div>;
      }

      default: {
        if (typeof data.value === 'number') {
          return renderNumericValue(
            conversionParams({ valueData: data.valueData }, utr, {
              value: data.value,
              unit: data.unit,
              numberScale: data.numberScale,
            }),
          );
        }
        return <div className='p-2 border rounded'>{data.value}</div>;
      }
    }
  };

  const getAggregationTypeLabel = (valueAggregation: ValueAggregation | undefined) => {
    return valueAggregation ? (valueAggregationMap[valueAggregation] ?? valueAggregation) : 'unknown';
  };

  return (
    <div className='d-flex flex-column gap-4'>
      {isUserStaff && (
        <div>
          <h5 className='d-flex align-items-center gap-2'>
            <FeatureStability key={'beta'} stability={'internal'} />
            <span>Differences</span>
            <button className='btn btn-sm btn-link p-0' onClick={() => setShowJson(!showJson)}>
              {showJson ? 'Hide JSON' : 'Show JSON'}
            </button>
          </h5>
          {showJson && (
            <div className='mt-2'>
              <div className='mb-2'>
                <strong>Current:</strong>
                <pre className='mt-1 p-2 bg-light rounded' style={{ maxHeight: '400px', overflow: 'auto' }}>
                  {JSON.stringify(current, null, 2)}
                </pre>
              </div>
              <div>
                <strong>Next:</strong>
                <pre className='mt-1 p-2 bg-light rounded' style={{ maxHeight: '400px', overflow: 'auto' }}>
                  {JSON.stringify(next, null, 2)}
                </pre>
              </div>
            </div>
          )}
        </div>
      )}
      <div>
        <h5>Current Data</h5>
        <p>
          Effective Date: {formatDateUTC(current.effectiveDate, DATE.AT_TIME)} <br />
          <span className='text-sm text-muted'>
            Aggregation Type: {getAggregationTypeLabel(current.valueAggregation)}
          </span>
        </p>
        {renderValue(current)}
      </div>
      <div>
        <h5>New Data</h5>
        <p>
          Effective Date: {formatDateUTC(next.effectiveDate, DATE.AT_TIME)} <br />
          <span className='text-sm text-muted'>Aggregation Type: {getAggregationTypeLabel(next.valueAggregation)}</span>
        </p>
        {renderValue(next)}
      </div>
    </div>
  );
};
