/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import React, { useRef } from 'react';
import {
  primitiveNumberTypes,
  getValueDataArray,
  UtrValueTypes,
  getValueListData,
  ValueDataArrayReturn
} from '@utils/universalTracker';
import { Button, Popover, PopoverBody, PopoverHeader } from 'reactstrap';
import { NotApplicableTypes, UtrvStatus } from '@constants/status';
import {
  renderNumericValue,
  conversionParams,
  getUnitCode,
  getNumberScale,
  isNaOrNr,
  getUnitValue,
  getListValueDataData,
} from '@utils/universalTrackerValue';
import TableInputView from '@components/survey/form/view/TableInputView';
import { tableDataToView } from '@utils/valueDataTable';
import UniversalTracker from '@models/UniversalTracker';
import { UniversalTrackerValuePlain } from '@g17eco/types/surveyScope';
import { ValueData } from '@components/survey/question/questionInterfaces';
import { SurveyModelMinimalUtrv, UnitConfig } from '@models/surveyData';
import { useUtrValueList } from '@hooks/useUtrValueList';
import { Option } from '@g17eco/types/valueList';
import { UtrValueType } from '@g17eco/types/universalTracker';
import { TOTAL_OPTION } from '@components/utr-modal/ColumnFilter';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';

export type MinimalUtrv = Pick<SurveyModelMinimalUtrv, '_id' | 'valueData' | 'status' | 'value'>

interface UniversalTrackerValueViewProps {
  utr: UniversalTracker;
  utrv: MinimalUtrv;
  valueType: string;
  className?: string;
  showTableDownload?: boolean;
  extractColumnCode?: string;
  classes?: { popover?: string };
  unitConfig?: UnitConfig;
}

interface ValueFiltered {
  extractColumnCode: string | undefined,
  valueType: string,
  utrv: MinimalUtrv,
  unitValue: number | undefined
}

const getValueFiltered = ({ extractColumnCode, valueType, utrv, unitValue }: ValueFiltered): string | number | undefined => {
  if (valueType === UtrValueTypes.numericValueList) {
    if (!extractColumnCode || extractColumnCode === TOTAL_OPTION.value) {
      return unitValue;
    }
    const data = getListValueDataData(utrv);
    if (typeof data === 'object' && !Array.isArray(data)) {
      return data[extractColumnCode];
    }
    return undefined;
  }

  return unitValue;
};

export const UniversalTrackerValueView = (props: UniversalTrackerValueViewProps) => {
  const {
    utr,
    utrv,
    valueType,
    className = 'text-ThemeAccentMedium',
    showTableDownload,
    extractColumnCode,
    classes = { popover: '.root-container' },
    unitConfig,
  } = props;

  const options = useUtrValueList(utr);
  const buttonRef= useRef<HTMLButtonElement>(null);
  const [showDetailPopup, setShowDetailsPopup] = React.useState(false);

  const unit = getUnitCode(utrv, utr);
  const numberScale = getNumberScale(utrv, utr);
  const unitValue = getUnitValue(utrv);

  // NA/NR
  const { valueData } = utrv;
  if (isNaOrNr(utrv)) {
    return <span className='badge bg-warning'>
      {valueData?.notApplicableType === NotApplicableTypes.nr ? 'N/R' : 'N/A'}
    </span>;
  }

  if (utrv.status === UtrvStatus.Created) {
    return null;
  }

  // Simple Values or numericaValueList gets a total in utrv.value so we can show that
  const valueTypeHasValue = primitiveNumberTypes.includes(valueType) || valueType === UtrValueTypes.numericValueList;
  if (valueTypeHasValue) {
    if (unitValue === undefined) {
      return null;
    }
    const valueFiltered = getValueFiltered({extractColumnCode, valueType, utrv, unitValue})
    return renderNumericValue(conversionParams(utrv, utr, {
      value: valueFiltered,
      unit,
      numberScale
    }), className, false);
  }

  if (!valueData) {
    return null;
  }

  if (valueType === UtrValueTypes.valueList) {
    const text = getValueListData(valueData.data, options);
    return <div className={'text-center text-truncate'}>
      <SimpleTooltip placement={'auto'} text={text}>
        {text}
      </SimpleTooltip>
    </div>
  }

  const values = getValueDataArray({
    utr: utr,
    data: valueData.data,
    value: unitValue,
    unit: unit,
    options,
    numberScale: getNumberScale(utrv, utr),
    displayCheckbox: undefined
  });

  if (valueType === UtrValueTypes.table && !valueData.table?.length) {
    return null;
  }

  const complexValue = getComplexText({
    values,
    valueType,
    valueData,
    utr,
    utrv,
    options,
    showTableDownload,
    extractColumnCode,
    unitConfig
  });

  if (!complexValue) {
    return null;
  }

  if (extractColumnCode) {
    return complexValue;
  }

  return (
    <div className='text-left'>
      <Button
        innerRef={buttonRef}
        color='link'
        type='button'
        onMouseEnter={() => setShowDetailsPopup(true)}
        onMouseLeave={() => setShowDetailsPopup(false)}
        onClick={() => setShowDetailsPopup(prev => !prev)}
      >
        <i className='fa fa-info-circle text-ThemeNeutralsDarkest' />
      </Button>
      {showDetailPopup ? (
        <Popover
          placement='bottom'
          target={buttonRef}
          isOpen={true}
          toggle={() => setShowDetailsPopup(!showDetailPopup)}
          className={'renderValueTableTooltip'}
          onClick={() => setShowDetailsPopup(!showDetailPopup)}
          container={classes.popover}
        >
          <PopoverHeader>{utr.getName()}</PopoverHeader>
          <PopoverBody onMouseEnter={() => setShowDetailsPopup(true)} onMouseLeave={() => setShowDetailsPopup(false)}>
            {complexValue}
          </PopoverBody>
        </Popover>
      ) : null}
    </div>
  );
}

interface RenderComplexTextProps {
  /** ValueList type values converted in to list options **/
  values: ValueDataArrayReturn[];
  valueType: string;
  valueData: ValueData;
  utr: UniversalTracker;
  utrv: Pick<UniversalTrackerValuePlain, 'value'>;
  options: Option[];
  showTableDownload?: boolean;
  extractColumnCode?: string;
  unitConfig?: UnitConfig;
}

const getComplexText = (p: RenderComplexTextProps) => {
  const {
    values,
    valueType,
    valueData,
    utr,
    utrv,
    options,
    showTableDownload,
    extractColumnCode,
    unitConfig
  } = p;

  if (!valueData) {
    return null;
  }
  switch (valueType) {
    case UtrValueType.Text: {
      if (!valueData.data) {
        return null;
      }
      return (
        <ul className='list-group text-left'>
          <li className='list-group-item'>{valueData.data as React.ReactNode}</li>
        </ul>
      );
    }
    case UtrValueType.ValueList:
    case UtrValueType.ValueListMulti: {
      if (!values) {
        return null;
      }
      return <ul className='list-group text-left'>
        {values.map((item) => <li key={item.label}
          className='list-group-item'>{item.value}</li>)}
      </ul>
    }
    case UtrValueType.TextValueList: {
      if (!values) {
        return null;
      }
      return <ul className='list-group text-left inline-values'>
        {values.map((item) => {
          const { label, value } = item;
          return <li key={label} className='list-group-item'>
            {getValueListData(label, options)}: <br /> <span
            className='strong text-primary'>{value}</span>
          </li>;
        })}
      </ul>
    }
    case UtrValueType.NumericValueList: {
      if (!values) {
        return null;
      }
      return <ul className='list-group text-left inline-values'>
        {values.map((item) => {
          const { label } = item;
          return <li key={label} className='list-group-item'>
            {getValueListData(label, options)}: <br /> {renderNumericValue(conversionParams(utrv, utr, item))}
          </li>;
        })}
      </ul>
    }
    case UtrValueType.Table: {
      if (!valueData.table?.length) {
        return null;
      }
      const { rows, editRowId } = tableDataToView(valueData);
      return <TableInputView
        universalTracker={utr}
        disabled={true}
        editRowId={editRowId}
        rowData={rows}
        showTableDownload={showTableDownload}
        extractColumnCode={extractColumnCode}
        unitConfig={unitConfig}
      />
    }
    case UtrValueType.Date: {
      if (!valueData.data) {
        return null;
      }
      return <span className='strong text-primary'>{valueData.data as React.ReactNode}</span>;
    }
    default:
      if (!values) {
        return null;
      }
      return <ul className='list-group text-left inline-values'>
        {
          values.map(({ value, unit, numberScale }, i) => {
            return <li key={i} className='list-group-item'>
              {renderNumericValue(
                conversionParams(utrv, utr, {
                  value,
                  unit,
                  numberScale
                })
              )}
            </li>;
          })
        }
      </ul>
  }
}
