/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { generateUrl, generateUrlWithQuery } from '../util';
import { ROUTES } from '../../constants/routes';
import { RootInitiativeData } from '../../types/initiative';
import { getDefaultConfig } from '../../config/app-config';
import { companyTrackerRouteMatcher, materialityTrackerRouteMatcher } from '../appRootMatcher';
import { CompanySettingsPages } from '../../apps/company-tracker/company-tracker-types';
import { isMaterialityTracker } from '@utils/apps';

export enum NotificationPage {
  // CT, CTL
  QuestionView = 'question_view',
  SurveyOverview = 'survey_overview',
  SurveyAssurance = 'survey_assurance',
  DataShareView = 'data_share_view',
  ReportView = 'report_view',
  ManageUsers = 'manage_users',
  SurveyOverviewRedirect = 'company_tracker_survey_redirector',
  SurveyTemplates = 'survey_templates',
  TemplateHistory = 'template_history',
  // Download reports generated by BackgroundJobs
  ReportDownload = 'report_download',

  IntegrationApp = 'integration_app',

  CompanySettings = 'company_tracker_settings',
  CompanySettingsSurveyConfig = 'company_tracker_settings_survey_config',
  CompanySettingsAccountManagement = 'company_tracker_settings_account_management',
  CustomMetrics = 'company_tracker_custom_metrics',
  NavigateByMap = 'company_tracker_navigate_by_map',
  BulkSurveyImport = 'bulk_survey_import',

  // Assurance
  AssurerDashboard = 'assurer_dashboard',
  AssurerPortfolio = 'assurer_portfolio',
  AssurerPortfolioQuestion = 'assurer_portfolio_question',

  // Portfolio
  PortfolioDataShareView = 'portfolio_data_share_view',

  // Materiality Tracker
  MaterialitySurveyOverview = 'assessment',
  MaterialityTrackerInsights = 'materiality_tracker_insights',
}

export interface CustomAttributes {
  page: NotificationPage;
  domain: string | undefined;
  initiativeId: string;
  orgId?: string;
  surveyId?: string;
  utrvId?: string;
  dataShareId?: string;
  portfolioId?: string;
  requesterId?: string;
  commentId?: string;
  filterByDelegationStatus?: string;
  onboardingId?: string;
  templateId?: string;
  templateHistoryId?: string;
  /** custom string, that can be forwarded to pendo **/
  pendo?: string;
  /** Support trigger using api when this query parameter is available **/
  pendoCategory?: string;
  jobId?: string;
  taskId?: string;

  /** Integration related params */
  integrationCode?: string;
  integrationStatus?: string;
}

const generateSearchParams = (params: (string | undefined)[][]) => {
  return params.reduce((acc, [value, key]) => {
    if (value) {
      return acc ? `${acc}&${key}=${value}` : `?${key}=${value}`;
    }
    return acc;
  }, '');
};

export const getRoute = (attr: CustomAttributes, rootAppPath: string | undefined): string | { pathname: string; search: string; } => {
  const { page, onboardingId, ...rest } = attr;

  const queryParams = {
    pendo: attr.pendo,
    pendoCategory: attr.pendoCategory,
  };

  switch (page) {
    case NotificationPage.QuestionView: {
      const questionUrl = `${generateUrl(ROUTES.COMPANY_TRACKER_SURVEY, {
        ...attr,
        page: 'question',
        rootAppPath,
      })}/${attr.utrvId ?? ''}`;

      const search = generateSearchParams([
        [attr.filterByDelegationStatus, 'filterByDelegationStatus[]'],
        [attr.commentId, 'commentId'],
      ]);

      return { pathname: questionUrl, search };
    }
    case NotificationPage.SurveyOverview: {
      const url = generateUrl(ROUTES.COMPANY_TRACKER_SURVEY, { ...attr, page: 'overview', rootAppPath });
      const search = generateSearchParams([
        [attr.filterByDelegationStatus, 'filterByDelegationStatus[]'],
        [attr.utrvId, 'utrvId'],
      ]);
      if (search) {
        return `${url}${search}`;
      }
      return url;
    }
    case NotificationPage.SurveyAssurance:
      return generateUrl(ROUTES.COMPANY_TRACKER_ASSURANCE, {
        ...attr,
        assuranceId: attr.portfolioId,
        page: 'assurance',
        rootAppPath
      });
    case NotificationPage.ManageUsers: {
      return generateUrlWithQuery(
        ROUTES.MANAGE_USERS,
        { ...rest, rootAppPath },
        { ...queryParams, onboardingId }
      );
    }
    case NotificationPage.CompanySettings: {
      return generateUrlWithQuery(
        ROUTES.ACCOUNT_SETTINGS,
        { ...rest, rootAppPath, page: CompanySettingsPages.Details },
        queryParams
      );
    }
    case NotificationPage.CompanySettingsSurveyConfig: {
      return generateUrlWithQuery(
        ROUTES.ACCOUNT_SETTINGS,
        { ...rest, rootAppPath, page: CompanySettingsPages.ReportSettings },
        queryParams
      );
    }
    case NotificationPage.CompanySettingsAccountManagement: {
      return generateUrlWithQuery(
        ROUTES.ACCOUNT_SETTINGS,
        { ...rest, rootAppPath, page: CompanySettingsPages.AccountManagement },
        queryParams
      );
    }
    case NotificationPage.CustomMetrics: {
      return generateUrlWithQuery(ROUTES.CUSTOM_METRICS, { ...rest, rootAppPath }, queryParams);
    }
    case NotificationPage.NavigateByMap: {
      return generateUrlWithQuery(ROUTES.NAVIGATE_BY_MAP, { ...rest, rootAppPath }, queryParams);
    }
    case NotificationPage.ReportView: {
      return generateUrl(ROUTES.COMPANY_TRACKER_LIST, { ...attr });
    }
    case NotificationPage.AssurerDashboard:
      return `${generateUrl(ROUTES.ASSURANCE, {
        ...attr,
        rootAppPath
      })}?portfolioId=${attr.portfolioId}`;
    case NotificationPage.AssurerPortfolio:
      return `${generateUrl(ROUTES.ASSURANCE_PORTFOLIO, {
        ...attr,
        assurancePortfolioId: attr.portfolioId,
        rootAppPath
      })}`;
    case NotificationPage.AssurerPortfolioQuestion: {
      const assurancePortfolioUrl = `${generateUrl(ROUTES.ASSURANCE_PORTFOLIO, {
        ...attr,
        assurancePortfolioId: attr.portfolioId,
        rootAppPath,
        questionId: attr.utrvId
      })}`;
      return { pathname: assurancePortfolioUrl, search: attr.commentId ? `?commentId=${attr.commentId}` : '' };
    }
    case NotificationPage.DataShareView:
      return `${generateUrl(ROUTES.DATA_SHARE_INITIATIVE, { ...attr, shareId: attr.dataShareId, rootAppPath })}`;
    case NotificationPage.PortfolioDataShareView: {
      const url = `${generateUrl(ROUTES.PORTFOLIO_TRACKER_PORTFOLIO, {
        ...attr,
        shareId: attr.dataShareId,
        portfolioId: attr.requesterId,
        rootAppPath
      })}`;
      return attr.dataShareId ? `${url}?share=${attr.dataShareId}` : url;
    }
    case NotificationPage.SurveyOverviewRedirect: {
      return generateUrlWithQuery(ROUTES.COMPANY_TRACKER_SURVEY_REDIRECTOR, {
        rootAppPath,
        initiativeId: attr.initiativeId,
      }, queryParams);
    }
    case NotificationPage.SurveyTemplates: {
      const surveyTemplatesUrl = `${generateUrl(ROUTES.SURVEY_TEMPLATES, {
        ...attr,
        rootAppPath
      })}`;
      return surveyTemplatesUrl;
    }
    case NotificationPage.TemplateHistory: {
      const historyUrl = `${generateUrl(ROUTES.SURVEY_TEMPLATES_HISTORY, {
        ...rest,
        historyId: attr.templateHistoryId,
        rootAppPath
      })}`;
      return historyUrl;
    }
    case NotificationPage.BulkSurveyImport:
      return generateUrl(ROUTES.BULK_IMPORTING, { initiativeId: attr.initiativeId });
    case NotificationPage.ReportDownload: {
      return generateUrlWithQuery(ROUTES.DOWNLOADS_PPTX, {
        ...attr,
        rootAppPath
      }, {
        action: 'download',
        jobId: attr.jobId,
        taskId: attr.taskId,
        surveyId: attr.surveyId,
      });
    }
    case NotificationPage.MaterialityTrackerInsights: {
      return generateUrlWithQuery(ROUTES.MATERIALITY_TRACKER_INSIGHTS, {
        ...attr,
        rootAppPath,
        insightSurveyId: attr.surveyId,
      }, {
        action: 'download',
        jobId: attr.jobId,
        taskId: attr.taskId
      });
    }
    case NotificationPage.IntegrationApp: {
      return generateUrlWithQuery(ROUTES.INTEGRATIONS_VIEW, {
        ...attr,
        rootAppPath,
        code: attr.integrationCode,
      }, {
        status: attr.integrationStatus,
      });
    }
    case NotificationPage.MaterialitySurveyOverview: {
      return generateUrl(ROUTES.MATERIALITY_TRACKER_SURVEY, { ...attr });
    }
    default:
      throw new Error(`${page} redirect is not supported`);
  }
}

export const getOrgConfiguration = (rootInitiatives: Pick<RootInitiativeData, '_id' | 'appConfig'>[], { orgId, initiativeId }: CustomAttributes) => {
  const id = orgId || initiativeId;
  const rootInitiative = rootInitiatives.find(o => o._id === id);

  if (rootInitiative) {
    return {
      rootAppPath: rootInitiative.appConfig?.rootAppPath ?? getDefaultConfig().branding.ctlApp.rootAppPath,
      storageKey: isMaterialityTracker(rootInitiative)
        ? materialityTrackerRouteMatcher.storageKey
        : companyTrackerRouteMatcher.storageKey,
      orgId: id,
    };
  }

  // This will need to be improved once other apps support switcher
  return { rootAppPath: 'company-tracker', storageKey: companyTrackerRouteMatcher.storageKey, orgId: id };
}
