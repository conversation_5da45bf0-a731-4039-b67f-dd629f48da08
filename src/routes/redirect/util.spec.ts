/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { CustomAttributes, getOrgConfiguration, getRoute, NotificationPage } from './util';
import { InitiativeTags, RootInitiativeData } from '../../types/initiative';
import { AppIds, CompanyTrackerIds, CompanyTrackerLightIds } from '../../utils/permission-groups';
import { getDefaultConfig } from '../../config/app-config';
import { CompanySettingsPages } from '../../apps/company-tracker/company-tracker-types';

type TestRootInitiativeData = Omit<RootInitiativeData, 'customer' | 'calculatedSubscriptions'>;

const created = (new Date()).toISOString();
describe('RedirectRoute util', function () {

  const initiativeId = '12312321-ctl';
  const initiativeIdPro = '12312321-pro';
  const base: CustomAttributes = {
    domain: undefined,
    orgId: initiativeId,
    initiativeId: initiativeId,
    page: NotificationPage.QuestionView
  }

  const baseCT: CustomAttributes = {
    domain: undefined,
    orgId: initiativeIdPro,
    initiativeId: initiativeIdPro,
    page: NotificationPage.QuestionView
  }

  const rootInitiativeLight: TestRootInitiativeData = {
    created,
    firstInitiativeId: initiativeId,
    name: '',
    _id: initiativeId,
    tags: [InitiativeTags.Organization],
    permissionGroup: CompanyTrackerLightIds[0],
  }

  const rootInitiativePro: TestRootInitiativeData = {
    created,
    firstInitiativeId: initiativeIdPro,
    name: '',
    _id: initiativeIdPro,
    tags: [InitiativeTags.Organization],
    permissionGroup: CompanyTrackerIds[0],
  }

  const ctl = getDefaultConfig().branding.ctlApp;
  const ctPath = 'company-tracker';


  describe('getOrgConfiguration', () => {

    it('should useAppConfig rootAppPath for company-tracker-light', function () {
      const { rootAppPath, storageKey, orgId } = getOrgConfiguration([rootInitiativeLight], base)
      expect(rootAppPath).toEqual(ctl.rootAppPath)
      expect(storageKey).toEqual(AppIds.CompanyTracker)
      expect(orgId).toEqual(rootInitiativeLight._id)
    });

    it('should useAppConfig rootAppPath company-tracker', () => {
      const { rootAppPath, storageKey, orgId } = getOrgConfiguration([rootInitiativePro], baseCT)
      expect(rootAppPath).toEqual('company-tracker')
      expect(storageKey).toEqual(AppIds.CompanyTracker)
      expect(orgId).toEqual(rootInitiativePro._id)
    });

    it('should fallback to initiativeId if orgId is not provided', () => {
      const customAttributes: CustomAttributes = {
        ...base,
        orgId: undefined,
      };
      const { orgId } = getOrgConfiguration([rootInitiativeLight], customAttributes);
      expect(orgId).toEqual(base.initiativeId);
    });

  })

  describe('getRoute fn', function () {

    it('should throw on unknown page', function () {
      const wrapper = () => getRoute({ ...base, page: 'random' as NotificationPage }, ctl.rootAppPath);
      expect(wrapper).toThrowError(/redirect is not supported/)
    });

    describe('CTL / CT Pro', () => {

      it('should generate Question View', () => {
        const attr = {
          ...base,
          surveyId: 'surveyId-1',
          utrvId: 'utrvId-1',
        };
        const url = getRoute(attr, ctPath);
        const expectedUrl = `/${ctPath}/reports/${attr.initiativeId}/${attr.surveyId}/question/${attr.utrvId}`;
        const expectedResult = {
          pathname: expectedUrl,
          search: '',
        }
        expect(url).toEqual(expectedResult);
      });

      it('should generate Question View', () => {
        const attr = {
          ...base,
          page: NotificationPage.SurveyOverview,
          surveyId: 'surveyId-1',
        };
        const url = getRoute(attr, ctPath);
        const expectedUrl = `/${ctPath}/reports/${attr.initiativeId}/${attr.surveyId}/overview`;
        expect(url).toEqual(expectedUrl)
      });

      it('should generate Question View with filter', () => {
        const attr: CustomAttributes = {
          ...base,
          page: NotificationPage.SurveyOverview,
          filterByDelegationStatus: 'usr-a',
          surveyId: 'surveyId-1',
        };
        const url = getRoute(attr, ctPath);
        const expectedUrl = `/${ctPath}/reports/${attr.initiativeId}/${attr.surveyId}/overview?filterByDelegationStatus[]=${attr.filterByDelegationStatus}`;
        expect(url).toEqual(expectedUrl)
      });

      it('should generate SurveyOverviewRedirect with pendo', () => {
        const attr: CustomAttributes = {
          ...base,
          page: NotificationPage.SurveyOverviewRedirect,
          pendo: 'setup-scope',
        };
        const url = getRoute(attr, ctPath);
        const expectedUrl = `/${ctPath}/redirect/${attr.initiativeId}/survey?pendo=${attr.pendo}`;
        expect(url).toEqual(expectedUrl)
      });

      it('should generate SurveyAssurance', () => {
        const attr: CustomAttributes = {
          ...base,
          page: NotificationPage.SurveyAssurance,
          surveyId: 'surveyId-1',
        };
        const url = getRoute(attr, ctPath);
        const expectedUrl = `/${ctPath}/reports/${attr.initiativeId}/${attr.surveyId}/assurance`;
        expect(url).toEqual(expectedUrl)
      });

      it('should generate SurveyAssurance with portfolioId', () => {
        const attr: CustomAttributes = {
          ...base,
          page: NotificationPage.SurveyAssurance,
          surveyId: 'surveyId-1',
          portfolioId: 'portfolio-1',
        };
        const url = getRoute(attr, ctPath);
        const expectedUrl = `/${ctPath}/reports/${attr.initiativeId}/${attr.surveyId}/assurance/${attr.portfolioId}`;
        expect(url).toEqual(expectedUrl)
      });

      it('should generate DataShareView', () => {
        const attr: CustomAttributes = {
          ...base,
          page: NotificationPage.DataShareView,
          dataShareId: 'dataShareId-1',
        };
        const url = getRoute(attr, ctPath);
        const expectedUrl = `/${ctPath}/admin/${attr.initiativeId}/data-share/${attr.dataShareId}`;
        expect(url).toEqual(expectedUrl)
      });


      it('should generate AccountSettings', () => {
        const attr: CustomAttributes = {
          ...base,
          page: NotificationPage.CompanySettings,
        };
        const url = getRoute(attr, ctPath);
        const expectedUrl = `/${ctPath}/admin/${attr.initiativeId}/account-settings/${CompanySettingsPages.Details}`;
        expect(url).toEqual(expectedUrl)
      });

      it('should generate AccountSettingsSurveyConfig', () => {
        const attr: CustomAttributes = {
          ...base,
          page: NotificationPage.CompanySettingsSurveyConfig,
        };
        const url = getRoute(attr, ctPath);
        const expectedUrl = `/${ctPath}/admin/${attr.initiativeId}/account-settings/${CompanySettingsPages.ReportSettings}`;
        expect(url).toEqual(expectedUrl)
      });

      it('should generate CustomMetrics', () => {
        const attr: CustomAttributes = {
          ...base,
          page: NotificationPage.CustomMetrics,
        };
        const url = getRoute(attr, ctPath);
        const expectedUrl = `/${ctPath}/admin/${attr.initiativeId}/custom-metrics`;
        expect(url).toEqual(expectedUrl)
      });

      it('should generate NavigateByMap', () => {
        const attr: CustomAttributes = {
          ...base,
          page: NotificationPage.NavigateByMap,
        };
        const url = getRoute(attr, ctPath);
        const expectedUrl = `/${ctPath}/map/${attr.initiativeId}`;
        expect(url).toEqual(expectedUrl)
      });

    });


    describe('Assurance tracker routes', function () {

      it('should generate AssurerPortfolio', () => {
        const attr: CustomAttributes = {
          ...base,
          page: NotificationPage.AssurerPortfolio,
          portfolioId: 'portfolioId-1'
        };
        const url = getRoute(attr, ctPath);
        const expectedUrl = `/assurance/portfolio/${attr.portfolioId}`;
        expect(url).toEqual(expectedUrl)
      });


      it('should generate AssurerPortfolioQuestion', () => {
        const attr: CustomAttributes = {
          ...base,
          page: NotificationPage.AssurerPortfolioQuestion,
          portfolioId: 'portfolioId-1',
          utrvId: 'portfolio-utrv-1',
        };
        const url = getRoute(attr, ctPath);
        const expectedUrl = `/assurance/portfolio/${attr.portfolioId}/${attr.utrvId}`;
        const expectedResult = {
          pathname: expectedUrl,
          search: '',
        }
        expect(url).toEqual(expectedResult);
      });

    });

    describe('Portfolio tracker routes', function () {

      it('should generate PortfolioDataShareView', () => {
        const attr: CustomAttributes = {
          ...base,
          page: NotificationPage.PortfolioDataShareView,
          dataShareId: 'dataShareId-1',
          requesterId: 'portfolioId-1'
        };
        const url = getRoute(attr, ctPath);
        const expectedUrl = `/portfolio-tracker/portfolio/${attr.requesterId}?share=${attr.dataShareId}`;
        expect(url).toEqual(expectedUrl)
      });

    });

  });


});
