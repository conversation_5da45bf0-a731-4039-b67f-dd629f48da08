/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { createSurveyData } from '@fixtures/survey-factory';
import { initiativeOne } from '@fixtures/initiative-factory';
import { createCombined } from '@fixtures/utr/utrv-factory';
import { QuestionGroup } from '@g17eco/types/survey';
import { TableColumnType, UtrValueType } from '@g17eco/types/universalTracker';
import { TableAggregationType, TableColumn } from '@components/survey/form/input/table/InputInterface';
import { valueListTestTable } from '@fixtures/value-list-factory';


export const utrvNumberOne = createCombined({ value: 100 }, {
  valueType: UtrValueType.Number,
  valueLabel: 'Number-One-Label',
  name: 'Number One',
  type: 'gri',
  typeCode: 'GRI-100',
  typeTags: ['gri-1', 'gri-1-102'],
});

export const utrvPercentageOne = createCombined({ value: 5 }, {
  valueType: UtrValueType.Percentage,
  valueLabel: 'Percentage-One-Label',
  name: 'Percentage One',
  type: 'gri',
  typeCode: 'GRI-100',
  typeTags: ['gri-1', 'gri-1-102'],
  valueValidation: {
    min: -100,
    max: 100,
  }
});

export const utrvPercentageTwo = createCombined({ value: 34 }, {
  valueType: UtrValueType.Percentage,
  valueLabel: 'Percentage-Two-Label',
  name: 'Percentage Two',
  type: 'gri',
  typeCode: 'GRI-100',
  typeTags: ['gri-1', 'gri-1-102'],
});

const columns: TableColumn[] = [
  {
    code: 'table_col_1',
    name: 'Number Col 1',
    shortName: 'Number Col 1',
    type: TableColumnType.Number,
  },
  {
    code: 'table_col_2',
    name: 'Number Col 2',
    shortName: 'Number Col 2',
    type: TableColumnType.Number,
    unitType: 'mass',
    unit: 'kg',
  },
  {
    code: 'table_col_3',
    name: 'Number Col 3',
    shortName: 'Currency Col 3',
    type: TableColumnType.Number,
    unitType: 'currency',
    unit: 'GBP',
    numberScale: 'million',
  },
]

const [col1, col2, col3] = columns;
export const utrvTableSingleRow = createCombined({
  valueData: {
    table: [
      [
        { code: col1.code, value: 101, },
        { code: col2.code, value: 102, unit: 'kg' },
        { code: col3.code, value: 103, unit: 'GBP', numberScale: 'million' },
      ],
    ],
    input: {
      table: [
        [
          { code: col1.code, value: 101, },
          { code: col2.code, value: 102_000, unit: 'g' },
          { code: col3.code, value: 0.103, unit: 'GBP', numberScale: 'billions' },
        ],
      ]
    }
  }
}, {
  valueType: UtrValueType.Table,
  valueLabel: 'Table-Single-Row-One-Label',
  name: 'Table Single Row One',
  type: 'gri',
  typeCode: 'GRI-100',
  typeTags: ['gri-1', 'gri-1-102'],
  valueValidation: { table: { columns, validation: { maxRows: 1 } } }
});


export const customMetricNestle = createCombined({
  status: 'verified',
  valueData: {
    table: [
      [
        {
          code: 'nestle-custom-kpi-20-1',
          value: 78,
          unit: 'GJ'
        }
      ]
    ]
  }
}, {
  valueType: UtrValueType.Table,
  valueLabel: 'Number-One-Label',
  name: 'Table',
  // type: 'custom_kpi',
  type: 'gri',
  typeCode: 'New - E1-5_37',
  valueValidation: {
    'table': {
      'validation': {
        'maxRows': 1
      },
      'columns': [
        {
          'type': TableColumnType.Number,
          'unitType': 'energy',
          'unit': 'GJ',
          'numberScale': 'millions',
          'code': 'nestle-custom-kpi-20-1',
          'name': 'Total energy consumption related to own operations (factories only) (GJ)',
          'unitInput': 'GJ',
          'numberScaleInput': 'single',
          'validation': {
            'decimal': 0
          }
        },
        {
          'type': TableColumnType.Number,
          'unitType': 'energy',
          'unit': 'MWh',
          'numberScale': 'millions',
          'code': 'nestle-custom-kpi-20-2',
          'name': 'Total energy consumption related to own operations (factories only) (MWh)',
          'unitInput': 'MWh',
          'numberScaleInput': 'single',
          'validation': {
            'decimal': 0
          }
        }
      ]
    }
  }
});


const numberColumn = {
  code: 'table_multi_col_1',
  name: 'Number Col 1',
  shortName: 'Number Col 1',
  type: TableColumnType.Number,
};
export const utrvTableMultipleRows = createCombined({
  valueData: {
    table: [[{ code: numberColumn.code, value: '100.00', }]],
    input: { table: [[{ code: numberColumn.code, value: '100.00', numberScale: 'single' }]] }
  }
}, {
  valueType: UtrValueType.Table,
  valueLabel: 'Table-Multiple-Rows-One-Label',
  name: 'Table Multiple Rows One',
  type: 'gri',
  typeCode: 'GRI-100',
  typeTags: ['gri-1', 'gri-1-102'],
  valueValidation: {
    table: {
      columns: [
        numberColumn
      ]
    }
  }
})

export const utrvTableRowGroupAggregator = createCombined(
  {
    valueData: {
      table: [
        [
          { code: 'type_of_data', value: valueListTestTable.options[0].code },
          { code: numberColumn.code, value: '100.00' },
        ],
      ],
      input: {
        table: [
          [
            { code: 'type_of_data', value: valueListTestTable.options[0].code },
            { code: numberColumn.code, value: '100.00', numberScale: 'single' },
          ],
        ],
      },
    },
  },
  {
    valueType: UtrValueType.Table,
    valueLabel: 'Table-Multiple-Rows-With-Row-Group-Aggregator-Label',
    name: 'Table Multiple Rows With Row Group Aggregator',
    type: 'gri',
    typeCode: 'GRI-100',
    typeTags: ['gri-1', 'gri-1-102'],
    valueValidation: {
      table: {
        columns: [
          {
            code: 'type_of_data',
            name: 'Type of data',
            type: TableColumnType.ValueList,
            listId: valueListTestTable._id,
          },
          numberColumn,
        ],
        aggregation: {
          type: TableAggregationType.Group,
          columns: [{ code: 'type_of_data' }],
        },
      },
    },
  },
);

const [optionOne, optionTwo] = valueListTestTable.options;
const colsWithVisibility: TableColumn[] = [
  {
    code: 'type_of_data',
    name: 'Type of data',
    type: TableColumnType.ValueList,
    listId: valueListTestTable._id,
  },
  {
    code: 'table_col_2',
    name: 'Number Col 2',
    shortName: 'Number Col 2',
    type: TableColumnType.Number,
    visibilityRule: {
      formula: `resolveString('{type_of_data}', '${optionOne.code}')`
    },
  },
  {
    code: 'table_col_3',
    name: 'Currency Col 3',
    shortName: 'Currency Col 3',
    unitType: 'currency',
    unit: 'USD',
    numberScale: 'millions',
    type: TableColumnType.Number,
    visibilityRule: {
      formula: `resolveString('{type_of_data}', '${optionTwo.code}')`
    },
  },
]

export const utrvTableMultipleVisibility = createCombined({
  valueData: { table: [] }
}, {
  valueType: UtrValueType.Table,
  valueLabel: 'Table-Multiple-Rows-visibility-rules',
  name: 'Table Multiple Rows Visibility rules',
  type: 'gri',
  typeCode: 'GRI-100',
  typeTags: ['gri-1', 'gri-1-102'],
  valueValidation: { table: { columns: colsWithVisibility } }
})

const valueDataWithVisibility = {
  table: [
    [
      { code: 'type_of_data', value: optionOne.code },
      { code: 'table_col_2', value: '111' },
      { code: 'table_col_3' },
    ],
    [
      { code: 'type_of_data', value: optionTwo.code },
      { code: 'table_col_2' },
      { code: 'table_col_3', value: '222', unit: 'USD', numberScale: 'millions' },
    ],
  ],
  input: {
    table: [
      [
        { code: 'type_of_data', value: optionOne.code },
        { code: 'table_col_2', value: '111' },
        { code: 'table_col_3' },
      ],
      [
        { code: 'type_of_data', value: optionTwo.code },
        { code: 'table_col_2' },
        { code: 'table_col_3', value: '222', numberScale: 'millions' },
      ],
    ],
  },
};

export const answeredUtrvTableMultipleVisibility = createCombined(
  {
    valueData: valueDataWithVisibility,
  },
  {
    valueType: UtrValueType.Table,
    valueLabel: 'Table-Multiple-Rows-visibility-rules',
    name: 'Table Multiple Rows Visibility rules',
    type: 'gri',
    typeCode: 'GRI-100',
    typeTags: ['gri-1', 'gri-1-102'],
    valueValidation: { table: { columns: colsWithVisibility } },
  }
);

// Order is important, when we use it for destructuring
const utrvs = [
  utrvNumberOne,
  utrvPercentageOne,
  utrvTableSingleRow,
];

const questionGroupOne: QuestionGroup = {
  groupName: 'Group 1',
  questions: utrvs.map(utrv => ({ utr: utrv.universalTracker })),
  list: [],
  subGroups: [],
};

const name = 'Survey-data-question-container-one';

// We should keep expanding this for question container tests
export const surveyDataFull = createSurveyData({
  name,
  initiativeId: initiativeOne._id,
  initiatives: [initiativeOne],
  fragmentUniversalTrackerValues: utrvs,
  questionGroups: [questionGroupOne],
});

