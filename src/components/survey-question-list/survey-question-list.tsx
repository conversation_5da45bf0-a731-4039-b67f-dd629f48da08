/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import { createRef, ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import { BaseScopeQuestion, ScopeQuestionOptionalValue } from '../../types/surveyScope';
import type { ScopeQuestionGroupOptionalValue } from '../../types/survey';
import { BulkActionToolbarProps, BulkActionToolbarSelectedUtrv } from './partials/BulkActionToolbar';
import { cloneDeep } from '@apollo/client/utilities';
import { SurveyModelMinimalUtrv } from '../../model/surveyData';
import { FlagProperties } from '../../services/G17Client';
import { QuestionListGroups } from './partials/QuestionListGroups';
import { SurveyQuestionListToolbarProps } from '../survey/survey-question-list-toolbar';
import { getQuestionGroupId, HighlightQuestion, useHighlightQuestion } from './utils';
import './survey-question-list.scss';
import { SessionStorage } from '../../services/SessionStorage';
import { useBookmarks } from './hooks/useBookmarks';
import { ScrollToTop } from '../button/ScrollToTop';
import type { QuestionListColumn } from '@g17eco/types/survey-question-list';

export type BulkActionProps = Omit<BulkActionToolbarProps, 'surveyData'>;

export interface SurveyQuestionListProps {
  allowBulkActions?: boolean;
  expandedQuestionGroupId?: string;
  resetExpandedQuestionGroup?: () => void;
  questionGroupRef?: React.RefObject<HTMLDivElement>;
  surveyId: string;
  initiativeId: string;
  surveyGroups: ScopeQuestionGroupOptionalValue[];
  disabledUTRs?: string[];
  handleNoRows?: () => void;
  noRowsMessage?: JSX.Element | string;
  goToQuestion?: (id: string, index?: number) => void;
  alternativeCode?: string;
  bulkActionToolbar?: (props: BulkActionProps) => ReactNode;
  columns: QuestionListColumn[];
  toolbar?: (
    props: Pick<
      SurveyQuestionListToolbarProps,
      | 'surveyId'
      | 'initiativeId'
      | 'toggleExpandAll'
      | 'isExpanded'
      | 'toggleSelectAll'
      | 'isSelectAll'
    >
  ) => ReactNode;
  renderExpansionRow?: (question: ScopeQuestionOptionalValue, rowIndex: number) => ReactNode;
  getDisabledRowTooltip?: (question: ScopeQuestionOptionalValue) => string | undefined;
}

const SurveyQuestionList = (props: SurveyQuestionListProps) => {
  const {
    surveyGroups,
    bulkActionToolbar,
    toolbar,
    surveyId,
    initiativeId,
    allowBulkActions = true,
    disabledUTRs,
    expandedQuestionGroupId,
    renderExpansionRow,
    getDisabledRowTooltip
  } = props;

  const [selectedQuestions, setSelectedQuestions] = useState<BulkActionToolbarSelectedUtrv[]>([]);
  const [questionGroupDisplay, setQuestionGroupDisplay] = useState<string[]>([]);
  const highlightQuestionRef = createRef<HTMLDivElement>();

  // selectedQuestions is filtered out by the latest disabledUTRs if filters changed
  useEffect(() => {
    setSelectedQuestions(prev => prev.filter(question => !disabledUTRs?.includes(question.universalTrackerId)));
  }, [disabledUTRs]);


  const toggleQuestionGroup = useCallback((id: string) => {
    setQuestionGroupDisplay(prev => {
      const newDisplay = [...prev];
      const index = newDisplay.indexOf(id);
      if (index >= 0) {
        newDisplay.splice(index, 1);
      } else {
        newDisplay.push(id);
      }
      return newDisplay;
    });
  }, []);

  const { isBookmarked, toggleBookmark } = useBookmarks(surveyId);

  const isHidden = useCallback(
    (question: BaseScopeQuestion) => {
      if (!Array.isArray(disabledUTRs) || disabledUTRs.length === 0) {
        return false;
      }

      return disabledUTRs.includes(question.universalTracker.getId());
    },
    [disabledUTRs]
  );

  const filteredSurveyGroups = useMemo(() => {
    const groups = cloneDeep(surveyGroups);
    let questionsCount = 0;

    for (let i = groups.length - 1; i >= 0; i--) {
      const group = groups[i];
      const isGroupHeading = !group.list.length;
      if (isGroupHeading) {
        group.count = questionsCount;
        questionsCount = 0;
      }

      const stack: Pick<ScopeQuestionGroupOptionalValue, 'list' | 'subGroups'>[] = [group];

      // iteratively process all groups and subgroups
      while (stack.length) {
        const currentGroup = stack.pop();
        if (currentGroup) {
          currentGroup.list = currentGroup.list.filter((q) => !isHidden(q));
          // push subgroups into the stack to process next
          stack.push(...(currentGroup.subGroups ?? []));
        }
      }

      questionsCount += group.list.length;
    }

    return groups;
  }, [isHidden, surveyGroups]);

  const highlight = useHighlightQuestion({ surveyGroups: filteredSurveyGroups })
  const highlightQuestion: HighlightQuestion | undefined = highlight ? {
    ...highlight,
    rowRef: highlightQuestionRef
  } : undefined;

  useEffect(() => {
    const groupId = highlightQuestion?.groupId || expandedQuestionGroupId;
    if (!groupId) {
      return;
    }
    setQuestionGroupDisplay((prev) => Array.from(new Set([...prev, groupId]).values()));
  }, [highlightQuestion?.groupId, expandedQuestionGroupId]);

  useEffect(() => {
    if (!highlightQuestion?.rowRef) {
      return;
    }
    const current = highlightQuestion?.rowRef.current;
    if (!current || current === null) {
      return;
    }
    setTimeout(() => {
      current.scrollIntoView({ block: 'center', behavior: 'smooth' });
      // Clear the session state
      SessionStorage.deleteQuestionIndex();
    }, 100);
  }, [highlightQuestion?.rowRef]);

  const questionCount = useMemo(() => {
    const counts = new Map();
    surveyGroups.forEach((questionGroup) => {
      const hasQuestions = questionGroup.list && questionGroup.list.length > 0;
      if (!hasQuestions) {
        return;
      }
      questionGroup.list.forEach(question => {
        if (question.utrv && !isHidden(question)) {
          return counts.set(question.utrv._id, 1)
        }
      })
    });
    return counts.size;
  }, [surveyGroups, isHidden]);

  const isSelectAll = useMemo(
    () => selectedQuestions.length > 0 && questionCount === selectedQuestions.length,
    [questionCount, selectedQuestions]
  );
  const isExpanded = useMemo(() => questionGroupDisplay.length > 0, [questionGroupDisplay]);

  const handleExpandAll = useCallback(() => {
    const questionGroups: string[] = [];
    surveyGroups.forEach((questionGroup, i) => {
      const hasQuestions = questionGroup.list && questionGroup.list.length > 0;
      if (!hasQuestions) {
        return;
      }
      const questionGroupId = getQuestionGroupId(i);
      questionGroups.push(questionGroupId);
    });
    return setQuestionGroupDisplay(questionGroups);
  }, [surveyGroups]);

  const handleSelectAll = useCallback(() => {
    handleExpandAll();
    const selectedQuestionsList: Map<string, SurveyModelMinimalUtrv> = new Map();
    surveyGroups.forEach((questionGroup) => {
      const hasQuestions = questionGroup.list && questionGroup.list.length > 0;
      if (!hasQuestions) {
        return;
      }
      questionGroup.list.forEach((question) => {
        if (question.utrv && !isHidden(question)) {
          const { utrv, universalTracker } = question;
          const utr = {
            name: universalTracker.getName(),
            valueType: universalTracker.getValueType(),
            valueValidation: universalTracker.getValueValidation(),
            unit: universalTracker.getMetricUnitCode(),
            unitType: universalTracker.getMetricUnitType(),
            numberScale: universalTracker.getNumberScale(),
          };
          return selectedQuestionsList.set(utrv._id, { ...utrv, utr });
        }
      });
    });
    return setSelectedQuestions(Array.from(selectedQuestionsList.values()));
  }, [
    handleExpandAll,
    surveyGroups,
    isHidden,
  ]);

  const handleUnselectAll = useCallback(() => {
    setSelectedQuestions([]);
  }, []);

  const handleCollapseAll = useCallback(() => setQuestionGroupDisplay([]), []);
  const toggleSelectAll = useCallback(
    () => isSelectAll ? handleUnselectAll() : handleSelectAll(),
    [isSelectAll, handleUnselectAll, handleSelectAll]
  );
  const toggleExpandAll = useCallback(
    () => isExpanded ? handleCollapseAll() : handleExpandAll(),
    [isExpanded, handleCollapseAll, handleExpandAll]
  );

  const triggerBulkAction = (properties: FlagProperties) => {
    if (selectedQuestions.length > 0) {
      const selectedQuestionsWithProperties = selectedQuestions.map((utrv) => ({ ...utrv, ...properties }));
      setSelectedQuestions(selectedQuestionsWithProperties);
    }
  };

  const toggleSelectQuestions = (utrvs: BulkActionToolbarSelectedUtrv[]) => {
    setSelectedQuestions(prev => {
      const isAllSelected = utrvs.every(utrv => prev.some(u => u._id === utrv._id));
      const uniqueMap = new Map<string, BulkActionToolbarSelectedUtrv>();
      prev.forEach(u => uniqueMap.set(u._id, u));

      if (isAllSelected) {
        // If everything selected, then this is an Unselect action
        utrvs.forEach(u => uniqueMap.delete(u._id));
        return Array.from(uniqueMap.values());
      }

      // If partially selected, then this is an Select-All action
      utrvs.forEach(u => uniqueMap.set(u._id, u));
      return Array.from(uniqueMap.values());
    });
  }

  const toggleSelectQuestion = (utrv: BulkActionToolbarSelectedUtrv) => toggleSelectQuestions([utrv]);

  const isQuestionSelected = (utrvId: string) => selectedQuestions.some((u) => u._id === utrvId);

  return (
    <>
      {toolbar ? (
        <div className='mb-2'>
          {toolbar({
            surveyId,
            initiativeId,
            toggleExpandAll,
            isExpanded,
            toggleSelectAll,
            isSelectAll,
          })}
        </div>
      ) : null}
      <QuestionListGroups
        {...{
          ...props,
          selectedQuestions,
          toggleSelectQuestion,
          questionGroupDisplay,
          isQuestionSelected,
          toggleQuestionGroup,
          highlightQuestion,
          isBookmarked,
          toggleBookmark,
          toggleSelectQuestions,
          renderExpansionRow,
          getDisabledRowTooltip
        }}
        surveyGroups={filteredSurveyGroups}
        enableBulkActions={allowBulkActions && Boolean(bulkActionToolbar)}
      />
      <div className='survey-question-list__sticky-toolbar'>
        <ScrollToTop
          style={{
            minHeight: selectedQuestions.length > 0 ? '8rem' : '3rem',
            transition: 'min-height 0.5s ease', 
          }}
        />
        {bulkActionToolbar
          ? bulkActionToolbar({
            surveyId,
            initiativeId,
            questionCount,
            selectedQuestions,
            triggerBulkAction,
            toggleSelectAll,
            handleClose: handleUnselectAll,
          })
          : null}
      </div>
    </>
  );
}

export default SurveyQuestionList;
