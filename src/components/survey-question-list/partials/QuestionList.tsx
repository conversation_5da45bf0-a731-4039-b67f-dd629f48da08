/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React, { Fragment, ReactNode } from 'react';
import { ScopeQuestionOptionalValue } from '../../../types/surveyScope';
import { SurveyQuestionListProps } from '../survey-question-list';
import { ColumnSelectbox } from './ColumnSelectbox';
import { LazyLoadingTrackingList } from './LazyLoadingTrackingList';
import { RowEmpty } from './RowEmpty';
import { BulkActionToolbarSelectedUtrv } from './BulkActionToolbar';
import { Row } from '@g17eco/molecules/tracking-list';
import { generateUrl } from '../../../routes/util';
import { ROUTES } from '../../../constants/routes';
import { useHistory, useLocation } from 'react-router-dom';
import { HighlightQuestion } from '../utils';
import { ColumnCommonProps } from '@g17eco/types/survey-question-list';

interface QuestionListProps
  extends Pick<SurveyQuestionListProps, 'columns' | 'goToQuestion' | 'initiativeId' | 'surveyId'> {
  questionGroupId: string;
  questionList: ScopeQuestionOptionalValue[];
  startIndex: number;
  toggleSelectQuestion: (utrv: BulkActionToolbarSelectedUtrv) => void;
  enableBulkActions: boolean;
  colour?: string;
  // @deprecated in favour of preferredAltCodes
  alternativeCode?: string;
  preferredAltCodes?: string[];
  highlightQuestion?: HighlightQuestion;
  isQuestionSelected: (utrvId: string) => boolean;
  rowRef?: React.ForwardedRef<HTMLDivElement>;
  isBookmarked: (utrvId: string) => boolean;
  toggleBookmark: (utrvId: string) => void;
  renderExpansionRow?: (question: ScopeQuestionOptionalValue, rowIndex: number) => ReactNode;
  getDisabledRowTooltip?: (question: ScopeQuestionOptionalValue) => string | undefined;
}

const highlightClass = 'highlight-blink';

const removeClass: React.MouseEventHandler<HTMLDivElement> = (e) => e.currentTarget.classList.remove(highlightClass);

export const QuestionList = (props: QuestionListProps) => {
  const {
    questionGroupId,
    toggleSelectQuestion,
    enableBulkActions,
    colour,
    startIndex,
    alternativeCode,
    preferredAltCodes,
    questionList,
    initiativeId,
    surveyId,
    highlightQuestion,
    isQuestionSelected,
    rowRef,
    isBookmarked,
    toggleBookmark,
    renderExpansionRow = null,
    getDisabledRowTooltip = undefined,
  } = props;

  const location = useLocation();
  const history = useHistory();

  const columns = props.columns;
  const style = colour ? { borderColor: colour } : {};

  const handleGoToQuestion = (id: string, questionIndex: number) => {
    if (props.goToQuestion) {
      return props.goToQuestion(id, questionIndex);
    }

    const surveyPath = generateUrl(ROUTES.COMPANY_TRACKER_SURVEY, {
      initiativeId,
      surveyId,
      page: 'question',
    });

    const url = `${surveyPath}/${id}/${questionIndex ?? ''}`;
    const searchParams = new URLSearchParams(location.search);

    history.push({
      pathname: url,
      search: searchParams.toString(),
    });
  };

  return (
    <LazyLoadingTrackingList
      className='questionList'
      style={style}
      forceShow={highlightQuestion?.groupId === questionGroupId}
    >
      {questionList.map((question, rowIndex) => {
        if (!question.utrv) {
          return <RowEmpty key={`${questionGroupId}-empty-${rowIndex}`} question={question} rowIndex={rowIndex} />;
        }

        const questionIndex = startIndex + rowIndex;
        const isHighlighted = highlightQuestion?.questionIndex === questionIndex;
        const utrvId = question.utrv._id;
        const columnProps: ColumnCommonProps = {
          surveyId,
          initiativeId,
          question,
          alternativeCode,
          preferredAltCodes,
          handleGoToQuestion: () => handleGoToQuestion(utrvId, questionIndex),
          isBookmarked: isBookmarked(utrvId),
          toggleBookmark: () => toggleBookmark(utrvId),
        };

        const key = `${questionGroupId}-${utrvId}-${rowIndex}`;
        const ref = highlightQuestion?.questionIndex === questionIndex ? rowRef : undefined;
        return (
          <Fragment key={key}>
            <Row
              ref={ref}
              tooltip={getDisabledRowTooltip?.(question)}
              onMouseEnter={removeClass}
              className={`clickable index-${questionIndex} ${question.utrv.status} ${
                isHighlighted ? highlightClass : ''
              }`}
            >
              {columns.map((el, ix) => React.createElement(el, { ...columnProps, key: `col-${ix}-${key}` }))}
              {enableBulkActions ? (
                <ColumnSelectbox
                  {...columnProps}
                  isSelected={isQuestionSelected(utrvId)}
                  toggleSelectQuestion={toggleSelectQuestion}
                  disabled={getDisabledRowTooltip?.(question) !== undefined}
                />
              ) : null}
            </Row>
            {renderExpansionRow?.(question, rowIndex)}
          </Fragment>
        );
      })}
    </LazyLoadingTrackingList>
  );
};
