/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { UniversalTrackerValueView } from '@features/universal-tracker-value';
import { ColumnCommonProps } from '@g17eco/types/survey-question-list';
import { Column } from '@g17eco/molecules/tracking-list';
import { UnitConfig } from '@models/surveyData';

interface ColumnValueProps extends ColumnCommonProps {
  unitConfig?: UnitConfig;
}

export const ColumnValue = (props: ColumnValueProps) => {
  const { question, unitConfig } = props;

  if (!question.utrv) {
    return null;
  }

  return (
    <Column className='d-none d-md-inline-block valueCol'>
      <UniversalTrackerValueView
        utr={question.universalTracker}
        utrv={question.utrv}
        valueType={question.valueType}
        unitConfig={unitConfig}
      />
    </Column>
  );
}
