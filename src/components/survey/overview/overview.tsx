/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React from 'react';
import { connect, ConnectedProps } from 'react-redux';
import { Button } from 'reactstrap';
import { Link, RouteComponentProps, withRouter } from 'react-router-dom';
import { AssuranceModal } from '../../assurance/assurance-modal';
import { loadBlueprintMapping } from '../../../actions/blueprints';
import { setOverviewMode, setShowingMappedMetrics } from '../../../slice/surveySettings';
import SurveyQuestionList from '../../survey-question-list/survey-question-list';
import { getSurveyGroupsSelector } from '../../../selectors/survey';
import { reloadSurveyListSummary } from '../../../actions/survey';
import { getCurrentUser, isStaff, isUserManagerByInitiativeId } from '../../../selectors/user';
import { SurveyContext, SurveyContextLoadedProps } from '../../survey-container/SurveyContainer';
import { ROUTES } from '../../../constants/routes'
import { generateDisabledUtrs, hasUTRVFilter } from '../utils/getDisableUtrs'
import { getSurveyFlexSearchMap } from '../../../selectors/blueprint';
import { RootState } from '../../../reducers';
import { SurveyActionData } from '../../../model/surveyData';
import { isRootOrg } from '../../../selectors/initiative';
import QuestionProgressContainer from './question-progress-container/index';
import { generateUrl } from '../../../routes/util';
import { getSurveyOverviewMode } from '../../../selectors/appConfig';
import { SurveySettings, SurveyType } from '../../../types/survey';
import { SurveyMessageModal } from '../../message-modal/MessageModal';
import { BulkActionToolbar } from '../../survey-question-list/partials/BulkActionToolbar';
import { ColumnCode } from '../../survey-question-list/partials/ColumnCode';
import { ColumnFlags } from '../../survey-question-list/partials/ColumnFlags';
import { ColumnValue } from '../../survey-question-list/partials/ColumnValue';
import { ColumnStatusIcon } from '../../survey-question-list/partials/ColumnStatusIcon';
import { getSurveyName } from '../../../utils/surveyData';
import { SurveyQuestionListToolbar } from '../survey-question-list-toolbar';
import { SurveyFloatingToolbarButton, getInitialSurveySettings, isAutoAggregatedSurvey, SurveySettingOption } from '@utils/survey';
import { SearchQuestions } from '../survey-question-list-toolbar/partials/SearchQuestions';
import { ScopeViewDropdown } from '../survey-question-list-toolbar/partials/ScopeViewDropdown';
import { ExpandAllToggle } from '../survey-question-list-toolbar/partials/ExpandAllToggle';
import { OutdatedSurveyAlert } from './OutdatedSurveyAlert';
import { ShowingBookmarksToggle } from '../survey-question-list-toolbar/partials/ShowingBookmarksToggle';
import './overview.scss';
import { FeaturePermissions } from '../../../services/permissions/FeaturePermissions';
import { endpoints } from '@api/metric-groups';
import { endpoints as bookmarksEndpoints } from '@api/bookmarks';
import { endpoints as initiativeUtrEndpoints } from '@api/initiative-universal-trackers';
import { endpoints as utrvsCommentEndpoints } from '@api/utrv-comments';
import { SurveyPermissions } from '../../../services/permissions/SurveyPermissions';
import { PACK, QUESTION, SURVEY } from '@constants/terminology';
import { ColumnBookmark } from '../../survey-question-list/partials/ColumnBookmark';
import { MappedMetricsToggle } from '../survey-question-list-toolbar/partials/MappedMetricsToggle';
import { ColumnCommentFlag, ColumnTitle } from '@g17eco/molecules/survey-question-list';
import { Option, SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { FilterToggle } from '@g17eco/molecules/filter-toggle';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { Divider } from '@g17eco/molecules/tracking-list';
import { ReportSwitcherContainer } from '@components/initiative/ReportSwitcherContainer';
import classNames from 'classnames';
import { getRootInitiativeMap, ConfigurationType } from '@features/question-configuration';
import { InitiativeUniversalTracker } from '@g17eco/types/initiativeUniversalTracker';
import { getQuestionGroupId } from '@components/survey-question-list/utils';
import { getRootConfig } from '@selectors/globalData';

type Props = PropsFromRedux & RouteComponentProps;

interface AssuranceModalState {
  isOpen: boolean;
  surveyId?: string;
  initiativeId?: string
}

interface State {
  questionCount: number;
  messageModal: { isOpen: boolean, surveyId: string | undefined };
  assuranceModal: AssuranceModalState;
  disabledUTRs: undefined | string[];
  sidebarSettings: SurveySettings;
  surveyUpdated: boolean;
  expandedQuestionGroupId: string | undefined;
  rootInitiativeUtrMap: Map<string, InitiativeUniversalTracker> | undefined;
}

export const getSurveySettingOptions = ({
  isCombinedReport,
  canAccessAssurance,
  isStaffUser
}: {
  isCombinedReport: boolean;
  canAccessAssurance: boolean;
  isStaffUser: boolean;
}) => {
  const isDisabledAssurance = !canAccessAssurance || (isCombinedReport && !isStaffUser);
  const assuranceOption = {
    label: (
      <div>
        <i className='fal fa-award mr-2' />
        Manage assurance
      </div>
    ),
    value: SurveySettingOption.ManageAssurance,
    isDisabled: isDisabledAssurance,
    tooltip: !canAccessAssurance ? 'Upgrade to Company Tracker Pro to access this feature' : '',
  };

  const options: Option[] = [
    {
      label: (
        <span>
          <i className='fal fa-cog mr-2' />
          Configuration & Scope
        </span>
      ),
      value: SurveySettingOption.Configuration,
    },
    {
      label: (
        <span>
          <i className='fal fa-envelope mr-2' />
          Message Users
        </span>
      ),
      value: SurveySettingOption.Message,
    },
  ];

  if (isCombinedReport) {
    if (isStaffUser) {
      options.push(assuranceOption);
    }
    return options;
  }

  options.push(
    {
      label: (
        <span>
          <i className='fal fa-file-import mr-2' />
          Import {SURVEY.SINGULAR} data
        </span>
      ),
      value: SurveySettingOption.Import,
    },
    assuranceOption
  );

  return options;
};

/** @TODO: should migrate this to function component
 * @deprecated loadBlueprintMapping, should migrate to use RTK blueprintsApi
 */
class SurveyOverview extends React.Component<Props> {

  static contextType = SurveyContext;

  state: State = {
    questionCount: 0,
    messageModal: {
      isOpen: false,
      surveyId: undefined
    },
    assuranceModal: {
      isOpen: false,
      surveyId: undefined,
      initiativeId: undefined,
    },
    disabledUTRs: undefined,
    sidebarSettings: getInitialSurveySettings(),
    surveyUpdated: false,
    expandedQuestionGroupId: undefined,
    rootInitiativeUtrMap: undefined,
  };

  componentDidMount() {
    this.updateSurveyState();
    this.updateDisabledUTRs();
    this.presetOverviewMode();
    this.navigateToGroupByUtrvId();

    const { surveyData } = this.context as SurveyContextLoadedProps;
    this.props.getBookmarks({surveyId: surveyData._id});
    this.props.getInitiativeUniversalTrackers(surveyData.initiativeId).then(({ data }) => {
      const rootInitiativeUtrMap = getRootInitiativeMap(data);
      this.setState({ rootInitiativeUtrMap });
    });
    this.props.getUtrvsCommentCount(surveyData._id);
  }

  componentDidUpdate(prevProps: Props) {
    const { sidebarSettings } = this.context as SurveyContextLoadedProps;

    this.updateSurveyState();

    const watchedProps = Object.keys(this.state.sidebarSettings) as (keyof SurveySettings)[];
    if (watchedProps.some(prop => sidebarSettings[prop] !== this.state.sidebarSettings[prop])) {
      this.setState({ sidebarSettings: sidebarSettings }, () => this.updateDisabledUTRs());
    }

    const reduxProps: (keyof Props)[] = ['surveyGroups', 'blueprintState', 'searchIndex']
    if (reduxProps.some((prop) => this.props[prop] !== prevProps[prop])) {
      this.updateDisabledUTRs();
    }
  }

  navigateToGroupByUtrvId = () => {
    const { utrvId, removeUtrvIdFromSearchParams } = this.context as SurveyContextLoadedProps;
    if (utrvId) {
      const groupIndex = this.props.surveyGroups.findIndex((group) => group.list.some((q) => q.utrv._id === utrvId));
      const expandedQuestionGroupId = getQuestionGroupId(groupIndex);
      this.setState({ expandedQuestionGroupId });
      removeUtrvIdFromSearchParams();
    }
  }

  updateSurveyState = () => {
    const { isLoaded, surveyData } = this.context as SurveyContextLoadedProps;
    if (!isLoaded || this.state.surveyUpdated) {
      return;
    }

    this.setState({ surveyUpdated: true }, () => {
      this.props.loadBlueprintMapping(surveyData.sourceName);
    });
  }

  presetOverviewMode = () => {
    if (this.props.surveyOverviewMode !== this.props.defaultSurveyOverviewMode) {
      // Reset to default mode on mount;
      this.props.setOverviewMode(this.props.defaultSurveyOverviewMode);
    }
  }

  handleAddAssurance = (survey: SurveyActionData) => {
    this.setState({
      assuranceModal: {
        ...this.state.assuranceModal,
        isOpen: true,
        surveyId: survey._id,
        initiativeId: survey.initiativeId,
      },
    });
  }

  toggleMessageModal = (surveyId?: string) => {
    this.setState({
      messageModal: {
        isOpen: !surveyId ? false : !this.state.messageModal.isOpen,
        surveyId: surveyId
      }
    });
  }

  toggleAssurance = (submitted?: boolean) => {
    if (!this.props.canAccessAssurance) {
      return;
    }

    this.setState({
      assuranceModal: {
        ...this.state.assuranceModal,
        isOpen: !this.state.assuranceModal.isOpen,
      },
    }, () => {
      if (submitted === true) {
        this.props.reloadSurveyListSummary();
      }
    });
  };

  updateDisabledUTRs = () => {
    const { surveyGroups, blueprintState, searchIndex, user, tags, bookmarks } = this.props;
    const { surveyData, sidebarSettings, questions } = this.context as SurveyContextLoadedProps;

    const {
      filterByGoal,
      searchText,
      filterByStatus,
      filterByRegulatory,
      filterByModules,
      filterByDelegationStatus,
      filterByTag,
      filterByBookmarks
    } = sidebarSettings;

    if (!hasUTRVFilter(sidebarSettings)) {
      return this.setState({ disabledUTRs: undefined, questionCount: questions.length });
    }

    const disabledUTRs = generateDisabledUtrs({
      surveyGroups,
      blueprint: blueprintState.data,
      filterByGoal,
      searchText,
      filterByStatus,
      filterByRegulatory,
      filterByModules,
      filterByDelegationStatus,
      filterByTag,
      filterByBookmarks,
      searchIndex,
      user,
      tags: tags.data,
      bookmarks: bookmarks.data,
      metricGroups: surveyData.customMetricGroups
    })
    const ids = this.getQuestionIds(disabledUTRs);
    this.setState({ disabledUTRs, questionCount: ids.length });
  }

  redirectToPage(initiativeId: string, surveyId: string, page?: string) {
    return this.props.history.push({
      pathname: generateUrl(ROUTES.COMPANY_TRACKER_SURVEY, { initiativeId, surveyId, page }),
      search: this.props.history.location.search,
    });
  }

  onClickScopeWheel = (groupId: string) => {
    this.setState({ expandedQuestionGroupId: groupId });
  }

  resetExpandedQuestionGroup = () => {
    this.setState({ expandedQuestionGroupId: undefined });
  }

  render() {
    const {
      surveyData,
      isLoaded: contextLoaded,
      isReadOnly,
      sidebarSettings,
      handleChangeSettings
    } = this.context as SurveyContextLoadedProps;
    const {
      blueprintState,
      surveyOverviewMode,
      isManager,
      canAccessAssurance,
      surveyGroups,
      canManage,
      isRootOrganization,
      isShowingMappedMetrics,
      setShowingMappedMetrics,
      utrvsCommentCount,
      isStaffUser
    } = this.props;

    const isLoaded = contextLoaded && blueprintState.loaded && !utrvsCommentCount.isLoading;

    const surveyScopeLink = contextLoaded
      ? generateUrl(ROUTES.COMPANY_TRACKER_SURVEY, {
        initiativeId: surveyData.initiativeId,
        surveyId: surveyData._id,
        page: 'scope',
      })
      : '';

    const noRowsMessage = hasUTRVFilter(sidebarSettings) ? (
      <>
        You have no {QUESTION.PLURAL} in scope matching your search. If you are an Admin you can add additional{' '}
        {SURVEY.SINGULAR} scope{' '}
        <Link to={surveyScopeLink}>
          <u>here</u>
        </Link>
        .
      </>
    ) : this.props.isManager ? (
      <>
        You have no {QUESTION.PLURAL} in scope. Please update your {SURVEY.SINGULAR} scope{' '}
        <Link to={surveyScopeLink}>
          <u>here</u>
        </Link>
        .
      </>
    ) : (
      <>
        There are no {QUESTION.PLURAL} to answer in this {SURVEY.SINGULAR}. Please contact your {SURVEY.SINGULAR} admin
        if you think there should be
        {QUESTION.PLURAL} assigned to you.
      </>
    );

    const isCombinedReport = surveyData.type === SurveyType.Aggregation;
    const isAutoCombinedReport = isAutoAggregatedSurvey(surveyData.type);

    const OverviewDropdown = () => {
      if (!isManager || isAutoCombinedReport) {
        return null;
      }

      const onSelect = (value: string | undefined | null) => {
        switch (value) {
          case SurveySettingOption.Configuration: {
            const page = isCombinedReport ? 'update-combined' : 'configuration';
            this.redirectToPage(surveyData.initiativeId, surveyData._id, page);
            break;
          }
          case SurveySettingOption.Message:
            this.toggleMessageModal(surveyData._id);
            break;
          case SurveySettingOption.Import:
            this.redirectToPage(surveyData.initiativeId, surveyData._id, 'import-export');
            break;
          case SurveySettingOption.ManageAssurance:
            this.handleAddAssurance(surveyData);
            break;
          default:
            return;
        }
      };

      return (
        <SelectFactory
          className='report-settings-container'
          selectType={SelectTypes.SingleSelect}
          placeholder={
            <span>
              <i className='fal fa-bars mr-2 text-ThemeTextMedium' />
              {SURVEY.CAPITALIZED_SINGULAR} settings
            </span>
          }
          options={getSurveySettingOptions({ isCombinedReport, canAccessAssurance, isStaffUser })}
          onChange={(option) => onSelect(option?.value)}
          value={null}
          isSearchable={false}
          isTransparent
          isFlexibleSize
          showDropdownIndicator={false}
        />
      );
    };

    const isSurveyComplete = Boolean(surveyData?.completedDate);

    return (
      <>
        <OutdatedSurveyAlert initiativeId={surveyData.initiativeId} surveyData={surveyData} />
        <div className='whiteBoxContainer overview-container'>
          <div className={classNames('w-100 d-flex align-items-center justify-content-between', { 'flex-wrap flex-md-nowrap flex-md-row-reverse': isManager && !isAutoCombinedReport })}>
            <OverviewDropdown />
            <ReportSwitcherContainer
              initiativeId={surveyData.initiativeId}
              selectedSurveyId={surveyData._id}
              onChange={(surveyId) => this.redirectToPage(surveyData.initiativeId, surveyId)}
              showAutomaticReport={false}
              classes={{
                container: 'report-switcher-container',
                select: 'w-100 mw-100 d-flex justify-content-between align-items-center text-ThemeAccentExtradark text-xl fw-semibold',
              }}
            />
          </div>
          <div>
            <LoadingPlaceholder className='mt-0 mb-1' count={1} height={39} isLoading={!isLoaded}>
              <QuestionProgressContainer
                surveyGroups={this.props.surveyGroups}
                config={surveyData.scopeConfig}
                onClickScopeWheel={this.onClickScopeWheel}
                readOnly={isCombinedReport || isAutoCombinedReport}
              />
            </LoadingPlaceholder>

            <div className='mt-1 question-list__wrapper' data-testid='survey-question-list'>
              <LoadingPlaceholder className='mt-0 mb-1' count={3} height={39} isLoading={!isLoaded}>
                <SurveyQuestionList
                  key={isShowingMappedMetrics ? 'mapped-metrics-view' : surveyOverviewMode}
                  expandedQuestionGroupId={this.state.expandedQuestionGroupId}
                  resetExpandedQuestionGroup={this.resetExpandedQuestionGroup}
                  surveyId={surveyData._id}
                  initiativeId={surveyData.initiativeId}
                  surveyGroups={surveyGroups}
                  disabledUTRs={this.state.disabledUTRs}
                  noRowsMessage={noRowsMessage}
                  allowBulkActions={!isReadOnly}
                  bulkActionToolbar={(props) => (
                    <>
                      {isReadOnly ? null : (
                        <BulkActionToolbar
                          {...props}
                          rootInitiativeUtrMap={this.state.rootInitiativeUtrMap}
                          surveyData={surveyData}
                          hiddenOptions={
                            canManage && isRootOrganization
                              ? [SurveyFloatingToolbarButton.Platform]
                              : [...Object.values(ConfigurationType), ...Object.values(SurveyFloatingToolbarButton)]
                          }
                        />
                      )}
                    </>
                  )}
                  columns={[
                    ColumnBookmark,
                    ColumnCode,
                    Divider,
                    ColumnTitle,
                    (props) => <ColumnFlags {...props}
                      rootInitiativeUtrMap={this.state.rootInitiativeUtrMap}
                      verificationRequired={surveyData.verificationRequired}
                      evidenceRequired={surveyData.evidenceRequired}
                      noteRequired={Boolean(surveyData.noteRequired)}
                    />,
                    (props) => <ColumnCommentFlag {...props} utrvsCommentCount={utrvsCommentCount.data} />,
                    Divider,
                    (props) => <ColumnValue {...props} unitConfig={surveyData.unitConfig} />,
                    Divider,
                    ColumnStatusIcon,
                  ]}
                  toolbar={(props) => (
                    <SurveyQuestionListToolbar
                      {...props}
                      settings={sidebarSettings}
                      handleChangeSettings={handleChangeSettings}
                      surveyScope={surveyData.scope}
                      isShowingBookmarks={sidebarSettings.filterByBookmarks}
                      toggleShowingBookmarks={(hasFilterByBookmarks: boolean) => handleChangeSettings('filterByBookmarks', hasFilterByBookmarks)}
                      components={{
                        top: [SearchQuestions, ScopeViewDropdown],
                        mid: [
                          (props) => (
                            <MappedMetricsToggle
                              overviewMode={props.defaultOverviewMode ?? surveyOverviewMode}
                              isChecked={isShowingMappedMetrics}
                              onChange={setShowingMappedMetrics}
                            />
                          ),
                          ShowingBookmarksToggle,
                        ],
                        bottom: [FilterToggle, ExpandAllToggle],
                      }}
                    />
                  )}
                />
              </LoadingPlaceholder>
            </div>

            {isManager && !isSurveyComplete && !(isCombinedReport || isAutoCombinedReport) ?
              <div className='add-survey-pack'>
                <Button outline className='add-survey-pack__button' onClick={() => this.redirectToPage(surveyData.initiativeId, surveyData._id, 'scope')}>
                  <i className='fa-solid fa-plus mr-2' />
                  Add {SURVEY.ADJECTIVE} {PACK.SINGULAR}
                </Button>
              </div>
              : null}
          </div>

          {this.renderAssuranceModal()}
          {this.renderMessageModal()}
        </div>
      </>
    );
  }

  private renderAssuranceModal() {
    const { isOpen, surveyId, initiativeId } = this.state.assuranceModal;
    if (!isOpen || !surveyId || !initiativeId) {
      return <></>
    }

    return <AssuranceModal
      toggle={this.toggleAssurance}
      isOpen={isOpen}
      surveyId={surveyId}
      initiativeId={initiativeId}
    />;
  }

  private renderMessageModal() {
    const { isOpen, surveyId } = this.state.messageModal;
    if (!isOpen || !surveyId) {
      return null;
    }

    const { surveyData } = this.context as SurveyContextLoadedProps;
    return (
      <SurveyMessageModal toggle={() => this.toggleMessageModal()} subject={getSurveyName(surveyData)} surveyId={surveyId} />
    );
  }

  private getQuestionIds(disabledUTRs: string[]) {
    const disabled = new Set(disabledUTRs);
    const ids = new Set<string>();
    const surveyGroups = this.props.surveyGroups;
    surveyGroups.forEach(group => {
      if (Array.isArray(group.list)) {
        group.list.forEach(q => {
          const id = q.utrv?._id;
          if (id && !disabled.has(q.universalTracker.getId())) {
            ids.add(id)
          }
        })
      }
    });
    return Array.from(ids);
  }
}

const mapStateToProps = (state: RootState) => {
  const rootConfig = getRootConfig(state);
  return {
    user: getCurrentUser(state),
    defaultSurveyOverviewMode: getSurveyOverviewMode(state),
    surveyOverviewMode: state.surveySettings.overviewMode,
    isShowingMappedMetrics: state.surveySettings.isShowingMappedMetrics,
    surveyGroups: getSurveyGroupsSelector(state),
    blueprintState: state.blueprint,
    isManager: isUserManagerByInitiativeId(state, state.survey.loaded ? state.survey.data.initiativeId : undefined),
    canManage: state.survey.loaded && state.currentUser.loaded && SurveyPermissions.canManage(state.survey.data, state.currentUser.data),
    searchIndex: getSurveyFlexSearchMap(state),
    isRootOrganization: isRootOrg(state),
    canAccessAssurance: FeaturePermissions.canAccessAssurance(rootConfig),
    tags: endpoints.getCustomTags.select(state.survey.loaded ? state.survey.data.initiativeId : '')(state),
    bookmarks: bookmarksEndpoints.getUtrvBookmarks.select({surveyId: state.survey.loaded ? state.survey.data._id : ''})(state),
    utrvsCommentCount: utrvsCommentEndpoints.getUtrvsCommentCountBySurveyId.select(state.survey.loaded ? state.survey.data._id : '')(state as any),
    isStaffUser: isStaff(state)
  };
};

const mapDispatchToProps = {
  loadBlueprintMapping,
  reloadSurveyListSummary,
  setOverviewMode,
  setShowingMappedMetrics,
  getBookmarks: bookmarksEndpoints.getUtrvBookmarks.initiate,
  getInitiativeUniversalTrackers: initiativeUtrEndpoints.getInitiativeUniversalTrackers.initiate,
  getUtrvsCommentCount: utrvsCommentEndpoints.getUtrvsCommentCountBySurveyId.initiate,
}

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>

export default withRouter(connector(SurveyOverview));
