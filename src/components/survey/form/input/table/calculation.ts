/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { InputColumn, TableColumn } from './InputInterface';
import { FormulaVariables, tryCalculation } from '../../../../../utils/formula';
import { convertValue } from '../../../../../utils/units';

function updateRow(cols: InputColumn[], column: InputColumn) {
  let found = false;
  const newCols = cols.map(c => {
    if (c.code === column.code) {
      found = true;
      return { ...c, ...column }
    }
    return c;
  })

  if (!found) {
    newCols.push(column);
  }
  return newCols;
}


export const recalculateRowColumns = (row: InputColumn[], column: InputColumn, calculationColumns: TableColumn[]) => {
  const newRowUpdate = updateRow(row, column);

  const hasCalculation = calculationColumns.length > 0;
  if (!hasCalculation) {
    return newRowUpdate;
  }

  const initialValue: { [key: string]: any } = {};
  const columnValues = newRowUpdate.reduce((a, c) => {
    a[c.code] = c.numberScale ? convertValue(c.value, c.numberScale, 'single') : c.value;
    return a;
  }, initialValue);

  const recalculated = calculationColumns
    .map(c => {
      return {
        code: c.code,
        value: tryCalculation({
          formula: c.calculation?.formula || '',
          variables: columnValues,
          fallback: '',
        }),
      }
    })

  return recalculated.reduce((a, c) => updateRow(a, c), newRowUpdate)
}

// check if formula returns a result
const calculateVisibility = (variables: FormulaVariables, columnDefinition?: TableColumn) => {
  if (!columnDefinition || !columnDefinition.visibilityRule?.formula) {
    return true;
  }
  const formula = columnDefinition.visibilityRule.formula || '';
  const result = tryCalculation({
    formula,
    variables,
    fallback: 0
  });
  return Boolean(result)
}

export interface VisibilityResult {
  [key:string]: boolean
}

export const isColumnVisible = (code: string, visibilityResults?: VisibilityResult) => {
  return !visibilityResults || visibilityResults[code] !== false // undefined is fine means no formula was calculated
}

export const hasChangedVisibility = (current: VisibilityResult, changed: VisibilityResult) => {
  if (Object.keys(current).length !== Object.keys(changed).length) {
    return true;
  }
  for (const prop in changed) {
    if (!Object.hasOwn(current, prop) || changed[prop] !== current[prop]) {
      return true;
    }
  }
  return false
}

// recalculate visibility of table configuration columns (separate from input columns)
// it can be a cascade of dependencies so if we hide a parent column we need to hide all formula dependent from it, hence is recursive
// it need to skip saving hidden values previously set and then hidden hence the filtering of input columns
export const recalculateVisibilityRules = (
  row: InputColumn[],
  visibilityRuleColumns: TableColumn[],
  currentVisibility: VisibilityResult,
): { visibleColumns: InputColumn[], visibleResults: VisibilityResult } => {

  const variables = row.reduce((vars, input) => {
    vars[input.code] = Array.isArray(input.value) ? 0 : input.value
    return vars
  }, {} as { [key: string]: string | number | undefined });

  // results will be saved in caller component for access to the input form
  const visibilityResults: VisibilityResult = {}
  const visibleRows = [...row].filter(c => {
    const isColumnVisible = calculateVisibility(variables, visibilityRuleColumns.find(vc => vc.code === c.code))
    visibilityResults[c.code] = isColumnVisible
    return isColumnVisible
  });

  // If anything has changed, we need to recalculate all the visible because of dependencies
  // only passing visible rows so formula don't evaluate again for hidden ones
  const merged = { ...currentVisibility, ...visibilityResults }

  if (hasChangedVisibility(merged, currentVisibility)) {
    return recalculateVisibilityRules(visibleRows, visibilityRuleColumns, merged);
  }
  visibilityRuleColumns.forEach((vc) => {
    const dataRow = visibleRows.find(r => r.code === vc.code);
    if (!dataRow) {
      // If it's not visible, we push the clear version of the column without value
      // but we must keep the original default unit and number scale for validation
      visibleRows.push({
        code: vc.code,
        numberScale: vc.numberScaleInput || vc.numberScale,
        // Omit unit when unitType is 'currency' to stay consistent with table question submission format
        unit: vc.unitType === 'currency' ? undefined : vc.unitInput || vc.unit,
      });
    }
  });

  return { visibleColumns: visibleRows, visibleResults: merged };
};




