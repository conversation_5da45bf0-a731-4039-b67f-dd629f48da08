/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React from 'react';
import {
  InputColumn,
  TableColumn,
  TableInputProps,
  ValueTable,
} from './InputInterface';
import TableFactory, { TableTypes } from './TableFactory';
import { InputPlaceholderText } from '../../../../../constants/inputs';
import { TableColumnType } from '@g17eco/types/universalTracker';
import { hasAtLeastOneRequiredColumn } from './tableUtils';

interface TableInputFormProps extends TableInputProps {
  updateColumn: (update: InputColumn) => void;
  row: InputColumn[];
  multiRow: boolean;
  isColumnVisible: (code: string) => boolean
  placeholder?: string;
}

function createLabel(column: TableColumn) {
  const { name, shortName, instructions, code } = column;
  const isRequired = column.validation?.required;

  return (
    <>
      <label htmlFor={code} className='strong form-check-label'>
        {name || shortName || ''}
        <span className='ml-1 text-ThemeDangerMedium'>{isRequired ? '*' : ''}</span>
      </label>
      {instructions && <div className={'input-instructions'}>
        {instructions}
      </div>}
    </>
  )
}

function getPlaceholder(column: TableColumn, isAutoCalculated: boolean, multiRow: boolean, placeholder?: string) {

  if (!multiRow && placeholder && ['N/R', 'N/A'].includes(placeholder)) {
    return placeholder;
  }

  if (isAutoCalculated) {
    return '';
  }

  if (column.type === TableTypes.number || column.type === TableColumnType.Percentage) {
    return InputPlaceholderText.numeric;
  }

  if ((column.options && column.options.length > 0) || column.listId) {
    const isValueListMulti = column.type === TableColumnType.ValueListMulti;
    if (column.validation?.allowCustomOptions) {
      return isValueListMulti ? InputPlaceholderText.dropdownMultiOrCreate : InputPlaceholderText.dropdownOrCreate;
    }
    return isValueListMulti ? InputPlaceholderText.dropdownMulti : InputPlaceholderText.dropdown;
  }

  return InputPlaceholderText.text;
}

const RequiredFieldInstruction = () => (
  <div className='text-ThemeTextDark mb-3'>
    Fields marked <span className='text-ThemeDangerMedium'>*</span> are required
  </div>
);

export const TableInputForm = (props: TableInputFormProps) => {

  const { tableConfiguration, row, placeholder, multiRow, isColumnVisible } = props;

  const valueDataData = Array.isArray(props.valueDataData) ? [...props.valueDataData] : [];

  const { columns }: ValueTable = tableConfiguration;
  return <div data-testid={'table-input-form'}  className='questionWithFields inputGroupContainer'>
    {hasAtLeastOneRequiredColumn(columns) ? <RequiredFieldInstruction /> : null}
    {columns.map((column) => {
      const inputColumn = row.find(d => d.code === column.code) || { code: column.code }
      const isAutoCalculated = Boolean(column.calculation?.formula);
      if (!isColumnVisible(column.code)) {
        return null
      }
      return TableFactory({
        ...props,
        valueDataData,
        column,
        inputColumn,
        isAutoCalculated,
        placeholder: getPlaceholder(column, isAutoCalculated, multiRow, placeholder),
        label: createLabel(column)
      });
    })}
  </div>;
}
