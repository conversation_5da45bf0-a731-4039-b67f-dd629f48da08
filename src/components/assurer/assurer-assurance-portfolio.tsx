/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import React, { useMemo, useState } from 'react';
import { Button, Modal, ModalBody, ModalHeader } from 'reactstrap';
import { ROUTES } from '../../constants/routes';
import { useHistory, useParams } from 'react-router';
import { generateUrl } from '../../routes/util';
import Dashboard, { DashboardRow, DashboardSection } from '@g17eco/molecules/dashboard';
import { AssuranceSummaryTitle } from '../assurance/assurance-summary-title';
import { AssuranceSummaryContainer } from '../assurance/assurance-summary-container';
import SurveyQuestionList, { BulkActionProps } from '../survey-question-list/survey-question-list';
import { useAppDispatch, useAppSelector } from '../../reducers';
import { AssurancePortfolioStatus, AssuranceStatus, OrganizationAssurancePortfolio } from '../../types/assurance';
import { formatDate } from '../../utils/date';
import { CompleteAssuranceModal } from './complete-assurance-modal';
import { downloadPortfolioBundle } from '../../actions/assurance';
import { getAnalytics } from '../../services/analytics/AnalyticsService';
import { AnalyticsEvents } from '../../services/analytics/AnalyticsEvents';
import { AssuranceQuestionView } from './assurance-question-view';
import { ManageStakeholderGroup } from './stakeholders/manage-stakeholder-group';
import TeamMemberContainer from './stakeholders/TeamMemberContainer';
import { getCurrentUser } from '../../selectors/user';
import NotAuthorised from '../../routes/not-authorised';
import { ActionsToolbar } from '../survey-question-list/partials/ActionsToolbar';
import { DownloadButton } from './toolbar-buttons/DownloadButton';
import { AssureButton } from './toolbar-buttons/AssureButton';
import { DelegateButton } from './toolbar-buttons/DelegateButton';
import { ColumnValue } from '../survey-question-list/partials/ColumnValue';
import { ColumnAssuranceUtrvStatus } from './partials/ColumnAssuranceUtrvStatus';
import { SurveyQuestionListToolbar } from '../survey/survey-question-list-toolbar';
import { ScopeView, ScopeViewDropdown } from '../survey/survey-question-list-toolbar/partials/ScopeViewDropdown';
import { useSurveyGroups } from '../../utils/survey';
import { ExpandAllToggle } from '../survey/survey-question-list-toolbar/partials/ExpandAllToggle';
import { Loader, BlockingLoader } from '@g17eco/atoms/loader';
import './assurer-assurance-portfolio.scss';
import { ColumnCode } from '../survey-question-list/partials/ColumnCode';
import { SurveyOverviewMode } from '../../slice/surveySettings';
import { addSiteAlert, SiteAlertColors } from '../../slice/siteAlertsSlice';
import { QUESTION } from '@constants/terminology';
import { ColumnBookmark } from '../survey-question-list/partials/ColumnBookmark';
import { SearchQuestions } from '../survey/survey-question-list-toolbar/partials/SearchQuestions';
import { useFilterQuestions } from '../../apps/assurance/hooks/useFilterQuestions';
import { skipToken } from '@reduxjs/toolkit/query';
import { BasicAlert } from '@g17eco/molecules/alert';
import { ColumnCommentFlag, ColumnTitle } from '@g17eco/molecules/survey-question-list';
import { Divider } from '@g17eco/molecules/tracking-list';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { AssuranceAction, hasAssurancePermission } from './permissions-utils';
import { useGetAssurancePortfolioQuery, useGetUserOrganizationQuery } from '../../apps/assurance/api/assurance';
import { QuestionListColumn } from '@g17eco/types/survey-question-list';

interface RouteParams {
  assurancePortfolioId: string;
  questionId?: string;
  index?: string;
}

export interface AssuranceSettingsType {
  text: string;
  tooltip: string;
  handler: () => void;
  icon?: string;
  isCompleted?: boolean;
  isDisabled?: boolean;
}

const renderPortfolioComplete = (assurancePortfolio: OrganizationAssurancePortfolio) => {
  if (assurancePortfolio.status !== AssurancePortfolioStatus.Completed) {
    return '';
  }

  const hist = assurancePortfolio.history;
  const assuredDate = formatDate(hist[hist.length - 1].created);
  return <>
    <i className='fa fa-history ml-5 mr-2' />Portfolio Completed on {assuredDate}
  </>
}

export const portfolioBundleDownload = (_id: string, portfolioInitiativeId: string) => {
  return downloadPortfolioBundle(_id)
    .then(async ([document]) => {
      if (!document || !document.url) {
        return;
      }

      window.open(document.url, '_blank', '');

      const analytics = getAnalytics();
      return analytics.track(AnalyticsEvents.SurveyDataDownloaded, {
        assurancePortfolioId: _id,
        initiativeId: portfolioInitiativeId,
        source: 'survey_assurance'
      });
    })
}

export const assurancePortfolioRoute = {
  id: 'assurance_portfolio',
  label: 'Assurance Tracker',
  tooltip: 'Assurance Tracker',
  path: '/assurance/portfolio/:assurancePortfolioId/:questionId?/:index?',
  icon: 'fa-folder-open',
  appPermissionId: 'app_assurance',
  exact: false,
  auth: true,
}

// @TODO move to apps/assurance
const AssurerAssurancePortfolio = () => {
  const dispatch = useAppDispatch();
  const history = useHistory();
  const currentUser = useAppSelector(getCurrentUser);
  const { assurancePortfolioId, questionId, index } = useParams<RouteParams>();

  const [assuranceModal, setAssuranceModal] = React.useState({ open: false, allowConfirm: false });
  const [isStakeholderOpen, setStakeholderOpen] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [assuranceOverviewMode, setAssuranceOverviewMode] = useState<SurveyOverviewMode>(
    SurveyOverviewMode.QuestionList
  );
  const {
    data: rawPortfolio,
    isFetching: isFetchingPortfolio,
    error: portfolioQueryError,
    refetch
  } = useGetAssurancePortfolioQuery(assurancePortfolioId ?? skipToken);

  // TODO: Create a different endpoint to get utrvs comment count by survey id for Assurance Tracker

  const { data: organization, isFetching, error: orgQueryError } = useGetUserOrganizationQuery();

  const handleSelectOverviewMode = (view: ScopeView) => setAssuranceOverviewMode(view.value);

  const toggle = () => setStakeholderOpen((isOpen) => !isOpen);

  const isCompleted = rawPortfolio?.status === AssurancePortfolioStatus.Completed;

  const assurancePortfolio = useMemo(() => {
    if (!rawPortfolio) {
      return undefined;
    }

    const {
      universalTrackerValueAssurances,
      surveyData: { fragmentUniversalTrackerValues },
    } = rawPortfolio;

    const utrvAssurances = universalTrackerValueAssurances.map((assurance) => {
      const utrv = fragmentUniversalTrackerValues.find((utrv) => utrv._id === assurance.utrvId);
      if (!utrv) {
        return assurance;
      }
      return { ...assurance, utrv };
    });

    return { ...rawPortfolio, universalTrackerValueAssurances: utrvAssurances };
  }, [rawPortfolio]);

  const lookupMap = useMemo(
    () =>
      new Map(
        assurancePortfolio?.universalTrackerValueAssurances.map((assurance) => [assurance.utrvId, assurance._id])
      ),
    [assurancePortfolio?.universalTrackerValueAssurances]
  );

  const surveyGroups = useSurveyGroups(assuranceOverviewMode, assurancePortfolio?.surveyData);

  const { filters, changeFilter, disabledUTRs } = useFilterQuestions({
    survey: assurancePortfolio?.surveyData,
    surveyGroups,
  });

  const isReadyToComplete = useMemo(() => {
    return (
      !isCompleted &&
      assurancePortfolio?.universalTrackerValueAssurances.some(
        (utrvAssurance) => utrvAssurance?.status === AssuranceStatus.Completed
      )
    );
  }, [isCompleted, assurancePortfolio?.universalTrackerValueAssurances]);

  if (portfolioQueryError) {
    return <BasicAlert type={'danger'}>{portfolioQueryError.message}</BasicAlert>;
  }

  if (orgQueryError) {
    return <BasicAlert type={'danger'}>{orgQueryError.message}</BasicAlert>;
  }

  if (!assurancePortfolio || !currentUser) {
    return <Loader />;
  }

  const hasAccess = hasAssurancePermission({
    user: currentUser,
    action: AssuranceAction.CanViewPortfolio,
    organization,
    assurancePortfolio,
  });

  if (!hasAccess) {
    return <NotAuthorised />;
  }

  const canManage = hasAssurancePermission({
    user: currentUser,
    action: AssuranceAction.CanManage,
    organization,
    assurancePortfolio,
  });

  const canAssure = hasAssurancePermission({
    user: currentUser,
    action: AssuranceAction.CanAssureMetrics,
    organization,
    assurancePortfolio,
  });

  const goToQuestion = (id: string, index?: number) => {
    const url = generateUrl(assurancePortfolioRoute, {
      assurancePortfolioId,
      questionId: id,
      index: String(index ?? ''),
    });
    history.push(url);
  };

  if (questionId) {
    return (
      <AssuranceQuestionView
        canAssure={canAssure}
        assurancePortfolio={assurancePortfolio}
        surveyGroups={surveyGroups}
        questionId={questionId}
        index={index}
        handleReload={refetch}
        unitConfig={assurancePortfolio.surveyData.unitConfig}
      />
    );
  }

  const setCompleteAssuranceModal = (reload = false, allowConfirm = false) => {
    if (reload) {
      refetch();
      setAssuranceModal({
        open: false,
        allowConfirm,
      });
    } else {
      setAssuranceModal({
        open: !assuranceModal.open,
        allowConfirm,
      });
    }
  };

  const settings: AssuranceSettingsType[] = [
    {
      text: 'Team members',
      tooltip: 'Add Assurance Team Members',
      icon: 'fa fa-user-plus',
      handler: toggle,
      isCompleted,
      isDisabled: !canAssure,
    },
    {
      text: 'Download all data & evidence',
      tooltip: 'Download all data and evidence',
      icon: 'fa fa-file-archive',
      isDisabled: !hasAccess,
      handler: () => {
        setLoading(true);
        portfolioBundleDownload(assurancePortfolio._id, assurancePortfolio.initiativeId)
        .catch(() => {
          dispatch(addSiteAlert({
            content: 'There was a problem downloading',
            color: SiteAlertColors.Danger
          }))
        })
        .finally(() => setLoading(false))
      },
    },
    {
      text: 'Complete Assurance',
      tooltip: 'Complete Assurance Tracker and Attach Statement',
      handler: () => setCompleteAssuranceModal(false, true),
      isCompleted,
      isDisabled: !canManage || !isReadyToComplete,
    },
  ];

  const renderSettings = () => {
    return settings.map((setting) =>
      setting.isCompleted ? null : (
        <SimpleTooltip
          key={setting.text}
          text={setting.tooltip}
          component={
            <Button
              className='ml-2 px-3 mb-2'
              onClick={setting.handler}
              outline={Object.hasOwn(setting, 'icon')}
              disabled={setting.isDisabled}
            >
              {setting.icon ? <i className={`${setting.icon} mr-2`} /> : null}
              {setting.text}
            </Button>
          }
        />
      )
    );
  };

  const renderActionToolbar = (props: BulkActionProps) => {
    const { selectedQuestions } = props;
    const listButtons = [
      { name: 'download', Component: DownloadButton, buttonProps: { assurancePortfolio, selectedQuestions } },
      {
        name: 'follow',
        Component: DelegateButton,
        buttonProps: { assurancePortfolio, currentUser, selectedQuestions, handleReload: refetch, canAssign: canManage },
      },
      {
        name: 'assure',
        Component: AssureButton,
        buttonProps: { assurancePortfolio, lookupMap, selectedQuestions, handleReload: refetch, canAssure },
      },
    ];
    return <ActionsToolbar {...props} buttons={listButtons} />;
  };

  const columns: QuestionListColumn[] = [
    ColumnBookmark,
    ColumnCode,
    ColumnTitle,
    Divider,
    (props) => <ColumnCommentFlag {...props} />,
    Divider,
    (props) => <ColumnValue {...props} unitConfig={assurancePortfolio.surveyData.unitConfig} />,
    Divider,
    (props) => (
      <ColumnAssuranceUtrvStatus {...props} AssuranceUtrvs={assurancePortfolio.universalTrackerValueAssurances} />
    ),
  ];

  return (
    <div className='flex-column'>
      {isFetching || isFetchingPortfolio ? <BlockingLoader /> : null}
      <div>
        <Dashboard>
          <div>
            <Button color='link' onClick={() => history.push(generateUrl(ROUTES.ASSURANCE))}>
              <i className='fa fa-arrow-circle-left mr-2' />
              Back to Dashboard
            </Button>
          </div>
        </Dashboard>
      </div>
      <Dashboard className='assuranceContainer'>
        <AssuranceSummaryTitle assurancePortfolio={assurancePortfolio} />
        <AssuranceSummaryContainer assurancePortfolio={assurancePortfolio} />

        <DashboardRow className='mt-3' title={`${QUESTION.CAPITALIZED_PLURAL} for assurance`} headingStyle={3}
          buttons={renderSettings()} />

        <DashboardSection padding={0}>
          <SurveyQuestionList
            allowBulkActions={canAssure}
            surveyId={assurancePortfolio.surveyId}
            initiativeId={assurancePortfolio.initiativeId}
            surveyGroups={surveyGroups}
            goToQuestion={goToQuestion}
            noRowsMessage={`No ${QUESTION.PLURAL} have been selected for assurance`}
            bulkActionToolbar={renderActionToolbar}
            columns={columns}
            disabledUTRs={disabledUTRs}
            toolbar={(props) => (
              <SurveyQuestionListToolbar
                {...props}
                settings={filters}
                handleChangeSettings={changeFilter}
                defaultOverviewMode={assuranceOverviewMode}
                handleSelectOverviewMode={handleSelectOverviewMode}
                surveyScope={assurancePortfolio?.surveyData?.scope}
                components={{
                  top: [SearchQuestions, ScopeViewDropdown],
                  bottom: surveyGroups.length > 1 ? [ExpandAllToggle] : [],
                }}
              />
            )}
          />
        </DashboardSection>
        <DashboardRow>
          <div className='text-right flex-fill d-flex align-items-baseline flex-wrap'>
            <div className='mr-auto mb-2'>{renderPortfolioComplete(assurancePortfolio)}</div>
          </div>
          <CompleteAssuranceModal
            assurancePortfolio={assurancePortfolio}
            allowConfirm={assuranceModal.allowConfirm}
            isOpen={assuranceModal.open}
            toggle={(completed?: boolean) => setCompleteAssuranceModal(completed)}
          />
        </DashboardRow>
      </Dashboard>
      <Modal isOpen={isStakeholderOpen} toggle={toggle} backdrop='static' className='modal-lg'>
        <ModalHeader toggle={toggle}>Team members</ModalHeader>
        <ModalBody>
          <TeamMemberContainer>
            <ManageStakeholderGroup
              canManage={canManage}
              canAssure={canAssure}
              assurancePortfolio={assurancePortfolio}
            />
          </TeamMemberContainer>
        </ModalBody>
      </Modal>
      {isLoading ? <BlockingLoader /> : null}
    </div>
  );
};

export default AssurerAssurancePortfolio;
