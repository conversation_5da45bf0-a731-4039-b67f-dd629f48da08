/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { useCallback } from 'react';
import NumberFormat from '../../utils/number-format';
import { ImageFiles } from './ImageFiles';
import { Loader } from '@g17eco/atoms/loader';
import { ToggleIcon } from './ToggleIcon';
import { EvidenceFile } from '../survey/question/questionInterfaces';
import './EvidenceContainer.scss';
import { AddToLibraryFn, HandleFileDescriptionFn } from '../../types/file';
import { EvidenceUrl } from './EvidenceUrl';
import { isNumericString } from '@utils/string';
import { FileDescription } from './FileDescription';
import { AddToLibraryCheckbox } from './AddToLibraryCheckbox';

interface FileContentProps {
  fileIndex: number,
  file: EvidenceFile,
  props: EvidenceContainerProps,
  content: JSX.Element,
  handleFileDescriptionAdd?: HandleFileDescriptionFn,
  handleAddToLibrary?: AddToLibraryFn;
  metadata?: {
    initiativeId: string;
  };
}

const getFileContent = (file: EvidenceFile) => {
  if ('metadata' in file) {
    const { name } = file.metadata;
    const status = file.status ?? '';
    const isDeleted = file.isDeleted ? 'is-deleted' : '';
    return (
      <>
        <i className={`fa utrv-status ${status} ${isDeleted} fa-file`} />
        {'ownerSubType' in file && file.ownerSubType ? <span>{file.ownerSubType}</span> : null}
        <span style={{ cursor: file.url ? 'pointer' : 'default' }} onClick={() => fileDownload(file.url)}>
          {name} (<NumberFormat value={file.size / 1024} decimalPlaces={1} suffix='kb' />)
        </span>
      </>
    );
  }
  const name = file.file.name;
  return (
    <>
      <i className={'fa utrv-status fa-file'} />
      {name} (<NumberFormat value={file.file.size / 1024} decimalPlaces={1} suffix='kb' />)
    </>
  );
};

function fileContainer({
  fileIndex,
  file,
  props,
  content,
  handleFileDescriptionAdd,
  handleAddToLibrary,
  metadata,
}: FileContentProps) {
  const isDocumentLibrary = 'ownerId' in file && file.ownerId === metadata?.initiativeId;
  return (
    <div className='file d-flex gap-1' key={`fileContainer_${fileIndex}`}>
      {props.toggleFile ? (
        <ToggleIcon
          i={fileIndex}
          file={file}
          allowToggle={props.allowToggle}
          saving={props.saving}
          toggleFile={props.toggleFile}
        />
      ) : null}
      {file.type === 'file' ? (
        <div className='d-flex flex-column flex-fill'>
          <div className='d-flex align-items-center gap-1'>
            {content}
            {isDocumentLibrary ? (
              <div className='text-sm text-ThemTextMedium'>Document library: Page references not supported yet</div>
            ) : (
              <FileDescription handleFileDescriptionAdd={handleFileDescriptionAdd} photo={file} isReadOnly={false} />
            )}
          </div>
          <AddToLibraryCheckbox file={file} handleAddToLibrary={handleAddToLibrary} />
        </div>
      ) : (
        <>{content}</>
      )}
    </div>
  );
}

function showEvidenceLink({ fileIndex, file, props }: Pick<FileContentProps, 'fileIndex' | 'file' | 'props'>) {
  if ('file' in file) {
    return file.file.name;
  }

  const isDeleted = file.isDeleted ? 'is-deleted' : '';
  const status = file.status ?? '';

  const content = (
    <div className='d-flex align-items-center gap-1 text-truncate'>
      <i className={`utrv-status ${status} fa fa-${file.public ? 'link' : 'lock'} ${isDeleted}`} />
      <EvidenceUrl file={file} />
      {file.description ? (
        <span className='file-description text-ThemeTextMedium'>{isNumericString(file.description)
          ? `Page ${file.description}`
          : file.description}</span>
      ) : null}
    </div>
  );
  return fileContainer({ fileIndex, file, props, content });
}

const getFileType = (file: EvidenceFile) => {
  const mimetype = 'metadata' in file ? file.metadata.mimetype : file.file.type;

  const res = mimetype.split('/');
  if (Array.isArray(res)) {
    return res[0];
  }
  return mimetype;
};

function fileDownload(url?: string) {
  if (url) {
    window.open(url, '_blank', '');
  }
}

function nonImageFile({
  fileIndex,
  file,
  props,
  handleFileDescriptionAdd,
  handleAddToLibrary,
  metadata,
}: Pick<FileContentProps, 'fileIndex' | 'file' | 'props' | 'handleFileDescriptionAdd' | 'handleAddToLibrary' | 'metadata'>) {
  if (file.type === 'link') {
    return showEvidenceLink({ fileIndex, file, props });
  }

  return fileContainer({
    fileIndex,
    file,
    props,
    content: getFileContent(file),
    handleFileDescriptionAdd: 'metadata' in file ? handleFileDescriptionAdd : undefined,
    handleAddToLibrary,
    metadata,
  });
}

interface EvidenceContainerProps {
  files: EvidenceFile[];
  toggleFile?: (i: number, file: EvidenceFile) => void;
  allowToggle: boolean;
  isLoaded: boolean;
  saving: boolean;
  handleFileDescriptionAdd?: HandleFileDescriptionFn;
  handleAddToLibrary?: AddToLibraryFn;
  isReadOnly?: boolean;
  metadata: {
    initiativeId: string;
  };
}

export const EvidenceContainer = (props: EvidenceContainerProps) => {
  const {
    files,
    toggleFile,
    allowToggle = false,
    isLoaded = true,
    isReadOnly = false,
    handleFileDescriptionAdd,
    metadata,
    handleAddToLibrary,
  } = props;

  const toggleWrapper = useCallback((_i: number, file: EvidenceFile) => {
    const index = files.indexOf(file);
    // Must find file from original array, due to split of images/files arrays
    if (index !== -1 && toggleFile) {
      toggleFile(index, file);
    }

  }, [files, toggleFile])

  if (!isLoaded) {
    return <div className='mt-2' style={{minHeight: '60px', position: 'relative'}}>
      <Loader />
    </div>;
  }

  const other: EvidenceFile[] = [];
  const photos: EvidenceFile[] = [];

  files.forEach(file => {
    if (file.type !== 'link' && getFileType(file) === 'image') {
      photos.push(file);
    } else {
      other.push(file);
    }
  })

  const nonImageProps = {
    ...props,
    toggleFile: toggleWrapper,
  };

  return (
    <div className='text-left existing-evidence-container mb-3'>
      <ImageFiles
        handleFileDescriptionAdd={handleFileDescriptionAdd}
        photos={photos}
        allowToggle={allowToggle}
        isReadOnly={isReadOnly}
        toggleFile={toggleFile ? toggleWrapper : undefined}
        metadata={metadata}
        handleAddToLibrary={handleAddToLibrary}
      />
      {other.map((file, index) =>
        nonImageFile({
          fileIndex: index,
          file,
          props: nonImageProps,
          handleFileDescriptionAdd,
          handleAddToLibrary,
          metadata,
        }),
      )}
    </div>
  );
}
