/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import React, { useEffect, useState } from 'react';
import {
  getInputValue,
  getNaType,
  getNumberScale,
  getUnitCode,
  getValueData,
  getValueDataProp,
  isNa,
  isNr,
  validateMinMax
} from '@utils/universalTrackerValue';
import { UtrValueTypes } from '@utils/universalTracker';
import { tableDataToView } from '@utils/valueDataTable';
import { NotApplicableTypes } from '@constants/status';
import { InputPlaceholderText } from '@constants/inputs';
import { Addons, BaseInputProps, HandleValueChange, InputProps } from '../survey/form/input/InputProps';
import InputFactory from '../survey/form/input/InputFactory';
import UniversalTracker from '../../model/UniversalTracker';
import { SurveyModelMinimalUtrv, UnitConfig } from '../../model/surveyData';
import './QuestionInput.scss';
import { DisplayCheckBox, InitialResettableQuestionState, TableDataInfo, ValueDataData } from '../survey/question/questionInterfaces';
import { getInitialState } from '../survey/question/QuestionReducer';
import { loggerMessage } from '../../logger';
import { getDisplayCheckboxAndOriginalValueData } from '@components/survey/question/questionUtil';

interface Props {
  utrv: Pick<SurveyModelMinimalUtrv, 'status' | 'value' | 'valueData'>;
  utr: UniversalTracker;
  handleUpdate?: (formData: InitialResettableQuestionState) => void;
  saving?: boolean;
  index?: number;
  displayCheckbox?: DisplayCheckBox,
  handleError?: (msg: string) => void;
  scrollToRef?: () => void;
  isReadOnly?: boolean;
  isDisabled?: BaseInputProps['isDisabled'];
  unitConfig?: UnitConfig;
  addons?: Addons;
}

export const getDefaultPlaceholder = (utr: UniversalTracker) => {
  switch (utr.getValueType()) {
    case 'valueList':
      return InputPlaceholderText.dropdown;
    case 'valueListMulti':
      return InputPlaceholderText.dropdownMulti;
    case 'numericValueList':
    case 'sample':
    case 'number':
    case 'percentage':
      return InputPlaceholderText.numeric;
    case 'textValueList':
    case 'text':
    case 'date':
    default:
      return InputPlaceholderText.text;
  }
}

function getValueDataPropName(utr: UniversalTracker) {
  return utr.getValueType() === 'table' ? 'table' : 'data';
}

export const QuestionInput = (props: Props) => {
  const {
    handleUpdate,
    utrv,
    utr,
    saving = false,
    index = 0,
    displayCheckbox = {},
    handleError = (msg) => loggerMessage(msg),
    unitConfig,
    scrollToRef = () => {},
    isReadOnly = false,
    isDisabled,
    addons
  } = props;

  const [formData, updateData] = useState<InitialResettableQuestionState>(getInitialState());

  useEffect(() => {
    handleUpdate?.(formData);
  }, [handleUpdate, formData]);

  useEffect(() => {
    updateData({
      ...getInitialState(),
      valueData: getValueData(utrv, utr.getValueType()),
      value: getInputValue(utrv),
      unit: getUnitCode(utrv, utr, unitConfig),
      numberScale: getNumberScale(utrv, utr, unitConfig as UnitConfig),
      displayCheckbox: getDisplayCheckboxAndOriginalValueData({ valueType: utr.getValueType(), utrv }).displayCheckbox,
    });
  }, [unitConfig, utr, utrv]);

  const { suffix, prefix } = { suffix: '', prefix: '' };
  const {
    value,
    valueData,
    unit,
    numberScale
  } = formData;

  const table = tableDataToView(valueData);
  const naType = getNaType(utrv);
  const isStateNA = naType === NotApplicableTypes.na;
  const valueDataData = getValueDataProp(valueData, utr.getValueType());
  const defaultPlaceholder = getDefaultPlaceholder(utr);

  const handleValueChange: HandleValueChange = ({ value, min, max }) => {
    if (value !== undefined) {
      const { errored, message } = validateMinMax(value, min, max);
      if (value === '') {
        value = undefined; // Prevent hasChanged detection from mixing up initial 'undefined' from ''
      }
      if (errored) {
        return updateData(currentState => ({
          ...currentState,
          value,
          errored,
          message
        }));
      }
    }
    updateData(currentState => ({
      ...currentState,
      isNA: false,
      value: value,
      errored: false,
      message: undefined,
    }));
  };

  const handleValueDataTotalUpdate = (universalTracker: UniversalTracker) => {
    const valueDataData = valueData.data;
    const valueList = universalTracker.getValueListOptions();

    if (typeof valueDataData === 'object' && !Array.isArray(valueDataData)) {
      const sumFields = valueList.reduce((acc, item) => {
        const valueDataDatum = valueDataData[item.code] || 0;
        const checkboxSelected = displayCheckbox[item.code];
        if (checkboxSelected && valueDataDatum) {
          if (acc === undefined) {
            acc = 0;
          }
          return acc + parseFloat(valueDataDatum as string);
        } else {
          return acc;
        }
      }, undefined as undefined | number);
      return handleValueChange({ value: sumFields });
    }

    handleValueChange({ value: undefined });
  }

  const handleCheckboxChange = (checkboxId: string, checked: boolean) => {
    if (!utr) {
      return;
    }

    displayCheckbox[checkboxId] = checked;
    const isNumericValueList = utr.isType(UtrValueTypes.numericValueList);
    updateData(currentState => {
      currentState.displayCheckbox[checkboxId] = checked;
      return currentState;
    });

    if (isNumericValueList) {
      handleValueDataTotalUpdate(utr);
    }
  }

  const handleUnitChange = (unit: string) => updateData(currentState => ({ ...currentState, unit }));
  const handleNumberScaleChange = (numberScale: string) => updateData(currentState => ({ ...currentState, numberScale }));

  const handleValueDataChange = (utr: UniversalTracker, valueDataData: ValueDataData) => {
    const isNumericValueList = utr.isType(UtrValueTypes.numericValueList);
    const propName = getValueDataPropName(utr);
    updateData(currentState => ({
      ...currentState,
      isNA: false,
      errored: false,
      message: undefined,
      valueData: {
        [propName]: valueDataData
      }
    }));

    if (isNumericValueList) {
      handleValueDataTotalUpdate(utr);
    }
  }

  const handleUpdateTable = (table: Partial<TableDataInfo>) => {
    updateData(currentState => ({
      ...currentState,
      isNA: false,
      errored: false,
      message: undefined,
      valueData: {
        ...currentState.valueData,
        table: table.rows?.map(row => row.data) ?? []
      }
    }));
  }

  const data = {
    universalTracker: utr,
    isDisabled: (props) => isReadOnly || Boolean(isDisabled?.(props)),
    handleError,
    handleCheckboxChange,
    handleValueDataChange: (valueData) => handleValueDataChange(utr, valueData),
    handleValueChange,
    handleUnitChange,
    handleNumberScaleChange,

    // Input values
    valueDataData,
    questionValue: isStateNA ? undefined : value,

    // Multi-row table
    table,
    updateTable: handleUpdateTable,

    placeholder: isNr(utrv) ? 'N/R' : isNa(utrv) ? 'N/A' : defaultPlaceholder,
    saving,
    prefix,
    suffix,
    index,
    unit,
    numberScale,
    displayCheckbox,
    scrollToRef,
    addons,
    status: utrv.status,
    unitConfig,
  } satisfies InputProps;

  return (
    <div className='question-input-container'>
      <div className={isReadOnly ? 'questionDisabledView' : ''}>{InputFactory(data)}</div>
    </div>
  );
}
