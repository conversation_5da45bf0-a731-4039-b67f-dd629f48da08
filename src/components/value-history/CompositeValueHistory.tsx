/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { PureComponent } from 'react';
import { connect, ConnectedProps } from 'react-redux';
import { Loader } from '@g17eco/atoms/loader';
import { loadValueInfoDetails } from '../../actions/universalTracker';
import { HistoryTimelineComponent } from './HistoryTimelineComponent';
import { Modal, ModalBody, ModalHeader, Nav, NavItem, NavLink, TabContent, TabPane } from 'reactstrap';
import UniversalTracker from '../../model/UniversalTracker';
import './value-history.scss';
import { getCurrentUser } from '../../selectors/user';
import { SourceTypes } from '../../constants/utr';
import { reloadSurvey } from '../../actions/survey';
import { RootState } from '../../reducers';
import { UtrvHistoryInfoInterface } from '../../types/universalTrackerValue';
import { SurveyType } from '@g17eco/types/survey';
import { BasicAlert } from '@g17eco/molecules/alert';

interface Props extends PropsFromRedux {
  isOpen: boolean;
  toggle: () => void;
  isReadOnly?: boolean;
  ids: string[];
}

interface State {
  userMap: Map<any, any>;
  loaded: boolean;
  error: boolean;
  errorMessage: string;
  data: UtrvHistoryInfoInterface[];
  providers?: { code: string; name: string }[];
  activeTab: string;
}

class CompositeValueHistory extends PureComponent<Props, State> {

  state: State = {
    userMap: new Map(),
    loaded: false,
    error: false,
    errorMessage: '',
    data: [],
    providers: undefined,
    activeTab: '',
  };

  componentDidMount() {
    this.loadData(this.props.ids);
  }

  componentDidUpdate(prevProps: Readonly<Props>) {
    if (prevProps.ids.join('') !== this.props.ids.join('')) {
      this.loadData(this.props.ids)
    }
  }

  loadData(ids: string[]) {
    this.setState({ loaded: false, error: false });
    this.props.loadValueInfoDetails(ids).then((result) => {
      // TODO: remove array check in the next sprint 4.23
      const data = Array.isArray(result) ? result : result.data;
      const providers = Array.isArray(result) ? undefined : result.providers;
      const activeTab = Array.isArray(data) && data[0] ? data[0]._id : '';
      this.setState({
        data,
        providers,
        activeTab,
        loaded: true,
        error: false,
        errorMessage: ''
      })
    }).catch(e => {
      this.setState({ loaded: true, error: true, errorMessage: e.message })
    });
  }

  tabToggle(tab: string) {
    if (this.state.activeTab !== tab) {
      this.setState({ activeTab: tab });
    }
  }

  render() {
    const { loaded, error, data, activeTab, errorMessage } = this.state;
    if (!loaded) {
      return <Loader />;
    }

    if (error || data.length === 0) {
      return this.renderContent(<BasicAlert type={'danger'}>{errorMessage}</BasicAlert>);
    }

    return this.renderContent(<>
      {data.length > 1 && this.getTabs(data, activeTab)}
      {this.getTabContent(activeTab, data)}
    </>);
  }

  renderContent(content: JSX.Element) {
    return <Modal className='modal-lg' isOpen={this.props.isOpen} toggle={this.props.toggle} backdrop='static'>
      <ModalHeader toggle={this.props.toggle}>Provenance</ModalHeader>
      <ModalBody>{content}</ModalBody>
    </Modal>;
  }

  onHistoryReload = () => {
    if (this.props.surveyState.surveyId) {
      this.props.reloadSurvey(this.props.surveyState.surveyId)
    }
    this.loadData(this.props.ids)
  }

  getTabContent(activeTab: string, data: UtrvHistoryInfoInterface[]) {
    const { isReadOnly, surveyState } = this.props;
    const surveyType = surveyState.loaded ? surveyState.data.type : SurveyType.Default;
    const unitConfig = surveyState.loaded ? surveyState.data.unitConfig : undefined;

    const userId = this.props.user?._id as string;
    if (data.length === 1) {
      const d = data[0];
      const isStakeholder = d.stakeholders.stakeholder.includes(userId);
      const utr = d.universalTracker[0];
      return <HistoryTimelineComponent
        universalTracker={new UniversalTracker(utr)}
        history={d.history}
        isReadOnly={isReadOnly}
        documents={d.documents}
        users={d.users}
        surveyType={surveyType}
        isStakeholder={isStakeholder}
        isFragment={d.sourceType === SourceTypes.FRAGMENT}
        utrvId={d._id}
        reload={this.onHistoryReload}
        providers={this.state.providers}
        unitConfig={unitConfig}
      />
    }

    return <div className='row'>
      <div className='col-12'>
        <TabContent activeTab={activeTab}>
          {data.map((d) => {
            const isStakeholder = d.stakeholders.stakeholder.includes(userId);
            const [utr] = d.universalTracker;
            const name = utr ? utr.name : undefined;
            return (
              <TabPane key={d._id} tabId={d._id}>
                {name && <h2 className='mt-0 mb-2'>{name}</h2>}
                <HistoryTimelineComponent
                  universalTracker={new UniversalTracker(utr)}
                  history={d.history}
                  documents={d.documents}
                  users={d.users}
                  surveyType={surveyType}
                  isStakeholder={isStakeholder}
                  isFragment={d.sourceType === SourceTypes.FRAGMENT}
                  utrvId={d._id}
                  reload={this.onHistoryReload}
                  providers={this.state.providers}
                  unitConfig={unitConfig}
                />
              </TabPane>
            )
          })}
        </TabContent>
      </div>
    </div>;
  }

  getTabs(data: UtrvHistoryInfoInterface[], activeTab: string) {
    return <Nav tabs className='mt-2 w-100 justify-content-center'>
      {data.map((d, i) => {
        const [utr] = d.universalTracker;
        const name = utr ? utr.name : i;
        return <NavItem key={d._id}>
          <NavLink
            className={activeTab === d._id ? 'active' : ''}
            style={{ cursor: 'pointer' }}
            onClick={() => this.tabToggle(d._id)}
          >
            {name}
          </NavLink>
        </NavItem>
      })}
    </Nav>;
  }
}

const mapDispatchToProps = {
  loadValueInfoDetails,
  reloadSurvey,
};

const mapStateToProps = (state: RootState) => ({
  user: getCurrentUser(state),
  surveyState: state.survey
});
const connectorFn = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connectorFn>

export default connectorFn(CompositeValueHistory);

