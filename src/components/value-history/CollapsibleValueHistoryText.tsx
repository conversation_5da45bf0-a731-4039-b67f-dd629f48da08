import { useTextCollapse } from '@hooks/useTextCollapse';
import { useRef, useState } from 'react';
import { ValueHistoryText } from './ValueHistoryText';
import { Button } from 'reactstrap';
import UniversalTracker from '@models/UniversalTracker';
import { ValueHistory } from '@g17eco/types/universalTrackerValue';
import './CollapsibleValueHistoryText.scss';
import { UnitConfig } from '@models/surveyData';

const HiddenBtn = ({ isHidden = true, setHidden }: { isHidden: boolean, setHidden: React.Dispatch<React.SetStateAction<boolean>> }) => {
  const text = isHidden ? 'Read more' : 'Read less';
  const icon = isHidden ? 'fa-angle-down' : 'fa-angle-up';
  return (
    <Button color='link-secondary' onClick={() => setHidden((prev) => !prev)}>
      <div className='text-sm py-1'>
        <i className={`fa ${icon} mr-1 text-sm`} />
        {text}
      </div>
    </Button>
  );
};

export const CollapsibleValueHistoryText = ({
  universalTracker,
  history,
  statusTextString,
  username,
  unitConfig,
}: {
  universalTracker: UniversalTracker;
  history: ValueHistory;
  statusTextString: string | JSX.Element;
  username: string;
  unitConfig?: UnitConfig;
}) => {
  const [isHidden, setHidden] = useState(true);
  const valueHistoryRef = useRef<HTMLDivElement | null>(null);

  const { height, className, isTruncated } = useTextCollapse({
    elementRef: valueHistoryRef,
    defaultHeight: 200,
    isHidden,
    untruncatedHeight: ''
  });

  return (
    <>
      <div ref={valueHistoryRef} className={className} style={{ height }}>
        {ValueHistoryText.getTextString({ universalTracker, history, statusTextString, username, unitConfig })}
      </div>
      {isTruncated ? <HiddenBtn isHidden={isHidden} setHidden={setHidden} /> : null}
    </>
  );
};
