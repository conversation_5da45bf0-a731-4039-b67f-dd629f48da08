/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import React, { Fragment } from 'react';
import {
  renderNumericValue,
  conversionParams,
  getListValueDataData,
  getInputValue,
  isUtrvAssuranceComplete,
  isUtrvAssuranceRestated,
  isUtrvPartialAssurance,
  isUtrvAssuranceRejected,
} from '@utils/universalTrackerValue';
import TableInputView from '@components/survey/form/view/TableInputView';
import { tableDataToView } from '@utils/valueDataTable';
import {
  ConversionData,
  UtrvAssuranceStatus,
  ValueHistory,
  ValueDataData,
  ValueDataObject,
} from '@g17eco/types/universalTrackerValue';
import UniversalTracker from '@models/UniversalTracker';
import { UniversalTrackerValuePlain } from '@g17eco/types/surveyScope';
import UniversalTrackerValue from '@models/UniversalTrackerValue';
import { NotApplicableReadableText, NotApplicableTypes } from '@constants/status';
import { UtrValueType } from '@g17eco/types/universalTracker';
import { UncontrolledCollapse } from 'reactstrap';
import {
  getTableConfiguration,
  isNumberOrTextValueListType,
  isSingleRowTableType,
  getValueListData,
  getValueListMultiData,
} from '@utils/universalTracker';
import { CollapsibleValueHistoryText } from './CollapsibleValueHistoryText';
import { UnitConfig } from '@models/surveyData';

type StatusTextString = string | React.JSX.Element;

export class ValueHistoryText {
  private static getValueText({
    history,
    statusTextString,
    universalTracker,
    className,
    username,
  }: {
    history: ValueHistory;
    statusTextString: StatusTextString;
    universalTracker: UniversalTracker;
    className?: string;
    username?: string;
  }) {
    const { value, sampleSize, valueData } = history;
    const inputValue = valueData?.input?.value ? valueData.input.value : value;
    const valueString = ValueHistoryText.getValueString(inputValue, history, valueData, universalTracker, className);

    const sampleSizeString =
      typeof sampleSize === 'number' ? (
        <React.Fragment>
          out of {ValueHistoryText.withUnits({ utrvLike: history, value: sampleSize, utr: universalTracker })}
        </React.Fragment>
      ) : (
        ''
      );

    return (
      <>
        <div data-testid='action-username'>
          {statusTextString} by <strong>{username}</strong>
        </div>
        <div data-testid='history-text'>
          {valueString} {sampleSizeString}
        </div>
      </>
    );
  }

  private static getValueString(
    value: number | undefined,
    h: ValueHistory,
    valueData: UniversalTrackerValuePlain['valueData'] | ValueDataData,
    utr: UniversalTracker,
    className?: string,
  ) {
    if (value === undefined) {
      return valueData &&
        typeof valueData === 'object' &&
        'notApplicableType' in valueData &&
        valueData.notApplicableType === NotApplicableTypes.nr
        ? NotApplicableReadableText[NotApplicableTypes.nr]
        : NotApplicableReadableText[NotApplicableTypes.na];
    }
    return ValueHistoryText.withUnits({ utrvLike: h, value, utr, className });
  }

  private static withUnits({
    utrvLike,
    value,
    utr,
    className = '',
    nonNumericClassName = 'strong text-primary',
  }: {
    utrvLike: UniversalTrackerValue | UniversalTrackerValuePlain | ValueHistory;
    value: number | string | undefined;
    utr: UniversalTracker;
    className?: string;
    nonNumericClassName?: string;
  }) {
    if (isNaN(value as number) || !utr || !utr.isNumeric()) {
      return <span className={nonNumericClassName}>{value}</span>;
    }

    const data = conversionParams(utrvLike, utr, { value } as Partial<ConversionData>);
    return renderNumericValue(data, className);
  }

  private static getValueDataText(
    username: string,
    universalTracker: UniversalTracker,
    h: ValueHistory,
    statusTextString: StatusTextString,
  ) {
    const options = universalTracker.getValueListOptions();
    const valueData: ValueDataData | undefined = h.valueData?.data
      ? Array.isArray(h.valueData?.data)
        ? getValueListMultiData(h.valueData.data, options)
        : getValueListData(h.valueData.data, options)
      : undefined;

    if (statusTextString === 'Created' && valueData === undefined) {
      return `New data point ${statusTextString}`;
    }

    const dataString = ValueHistoryText.getDataString(valueData, h);

    return (
      <>
        <div data-testid='action-username'>
          {statusTextString} by <strong>{username}</strong>
        </div>
        <div data-testid='history-text'>{dataString}</div>
      </>
    );
  }

  private static getDataString(valueData: ValueDataData | undefined, h: ValueHistory): React.JSX.Element | string {
    if (valueData === undefined) {
      return <div className='text-ThemeWarningMedium'>{NotApplicableReadableText[NotApplicableTypes.na]}</div>;
    }
    if (h.valueData && h.valueData.notApplicableType === NotApplicableTypes.nr) {
      return <div className='text-ThemeWarningMedium'>{NotApplicableReadableText[NotApplicableTypes.nr]}</div>;
    }
    if (Array.isArray(valueData)) {
      return (
        <ul className='text-ThemeTextMedium'>
          {valueData.map((d) => (
            <li key={d}>{d}</li>
          ))}
        </ul>
      );
    }

    if (typeof valueData === 'object') {
      return (
        <ul className='text-ThemeTextMedium'>
          {Object.keys(valueData).map((k) => (
            <li key={k}>{valueData[k]}</li>
          ))}
        </ul>
      );
    }

    return <div className='text-ThemeTextMedium'>{valueData}</div>;
  }

  private static getTableView({
    username,
    universalTracker,
    history,
    statusTextString,
    unitConfig,
  }: {
    username: string;
    universalTracker: UniversalTracker;
    history: ValueHistory;
    statusTextString: StatusTextString;
    unitConfig?: UnitConfig;
  }) {
    const { rows, editRowId } = tableDataToView(history.valueData);

    if (statusTextString === 'Created') {
      return `New data point ${statusTextString}`;
    }

    if (rows.length > 0) {
      return (
        <>
          <div data-testid='action-username'>
            {statusTextString} by <strong>{username}</strong>
          </div>
          <TableInputView
            className={'p-0'}
            universalTracker={universalTracker}
            disabled={true}
            editRowId={editRowId}
            rowData={rows}
            ignoreType={true}
            unitConfig={unitConfig}
          />
        </>
      );
    }
    const dataString =
      history.valueData && history.valueData.notApplicableType === NotApplicableTypes.nr
        ? NotApplicableReadableText[NotApplicableTypes.nr]
        : NotApplicableReadableText[NotApplicableTypes.na];
    return (
      <>
        <div data-testid='action-username'>
          {statusTextString} by <strong>{username}</strong>
        </div>
        <div data-testid='history-text' className='text-ThemeWarningMedium'>
          {dataString}
        </div>
      </>
    );
  }

  private static getValueDataList(
    username: string,
    universalTracker: UniversalTracker,
    h: ValueHistory,
    statusTextString: StatusTextString,
  ) {
    let dataString: StatusTextString = '';

    const valueData = getListValueDataData(h) as ValueDataObject;
    const valueKeys = valueData ? Object.keys(valueData) : undefined;

    if (statusTextString === 'Created' && valueData === undefined) {
      return `New data point ${statusTextString}`;
    }

    const isNumericList = universalTracker.getValueType() === UtrValueType.NumericValueList;
    switch (true) {
      case Array.isArray(valueKeys) && valueKeys.length > 0: {
        const inputValue = getInputValue(h);
        const options = universalTracker.getValueListOptions();
        dataString = (
          <ul className={`keyValue ${isNumericList ? 'inline-rows' : ''}`}>
            {valueKeys.map((k: string) => (
              <li key={k}>
                <div className='value-label fw-semibold text-ThemeHeadingMedium'>{getValueListData(k, options)}:</div>
                <div className={'value-data-value text-ThemeTextMedium'}>
                  {ValueHistoryText.withUnits({
                    utrvLike: h,
                    value: valueData[k],
                    utr: universalTracker,
                    nonNumericClassName: '',
                  })}
                </div>
              </li>
            ))}

            {isNumericList && (
              <li key={'total'}>
                <div className='value-label strong text-ThemeHeadingMedium'>Total:</div>
                <div className={'value-data-value text-ThemeTextMedium'}>
                  {ValueHistoryText.getValueString(inputValue, h, valueData, universalTracker)}
                </div>
              </li>
            )}
          </ul>
        );
        break;
      }
      case h.valueData && h.valueData.notApplicableType === NotApplicableTypes.nr:
        dataString = (
          <span className='text-ThemeWarningMedium'>{NotApplicableReadableText[NotApplicableTypes.nr]}</span>
        );
        break;
      case valueData === undefined:
      default:
        dataString = (
          <span className='text-ThemeWarningMedium'>{NotApplicableReadableText[NotApplicableTypes.na]}</span>
        );
        break;
    }

    return (
      <>
        <div data-testid='action-username'>
          {statusTextString} by <strong>{username}</strong>
        </div>
        <div data-testid='history-text'>{dataString}</div>
      </>
    );
  }

  public static getTextString({
    universalTracker,
    history,
    statusTextString,
    username,
    unitConfig,
  }: {
    universalTracker: UniversalTracker;
    history: ValueHistory;
    statusTextString: StatusTextString;
    username: string;
    unitConfig?: UnitConfig;
  }) {
    const valueType = history.valueType ?? universalTracker.getValueType();
    switch (valueType) {
      case 'date':
      case 'text':
      case 'valueList':
      case 'valueListMulti':
        return ValueHistoryText.getValueDataText(username, universalTracker, history, statusTextString);
      case 'textValueList':
      case 'numericValueList':
        return ValueHistoryText.getValueDataList(username, universalTracker, history, statusTextString);
      case 'table':
        return ValueHistoryText.getTableView({ username, universalTracker, history, statusTextString, unitConfig });
      case 'number':
      case 'percentage':
      case 'sample':
      default:
        return ValueHistoryText.getValueText({ history, statusTextString, universalTracker, username });
    }
  }

  public static partialAssuranceProvenance(params: { history: ValueHistory; universalTracker: UniversalTracker }) {
    const { history, universalTracker } = params;

    if (history.assuranceStatus !== UtrvAssuranceStatus.Partial) {
      return null;
    }
    const { valueData, assuranceFields = [] } = history;
    if (isNumberOrTextValueListType(universalTracker)) {
      const data = valueData?.data ?? {};
      const listOptions = universalTracker.getValueListOptions();

      const assuredListItems = Object.entries(data)
        .map(([code, value]) => {
          const isAssured = assuranceFields.some((f) => f.code === code);
          if (isAssured) {
            const option = listOptions.find((o) => o.code === code);
            const valueDisplay = this.withUnits({
              utrvLike: history,
              value,
              utr: universalTracker,
              className: 'text-ThemeTextMedium',
              nonNumericClassName: 'text-ThemeTextMedium',
            });
            return (
              <li key={code}>
                <div className='fw-semibold text-ThemeHeadingMedium'>{option?.name ?? code}:</div>
                <div className={'text-ThemeTextMedium'}>{valueDisplay}</div>
              </li>
            );
          }
          return null;
        })
        .filter(Boolean);

      return <ol className='partial-assurance-list pl-0 m-0'>{assuredListItems}</ol>;
    }

    const columns = getTableConfiguration(universalTracker)?.columns ?? [];

    if (isSingleRowTableType(universalTracker)) {
      // Single row table store data in the first row
      const data = valueData?.table?.[0] ?? [];

      const assuredColumnItems = data
        .map(({ code, value }) => {
          const isAssured = assuranceFields.some((f) => f.code === code);
          if (isAssured) {
            const column = columns.find((c) => c.code === code);
            const valueDisplay = Array.isArray(value)
              ? value.join(', ')
              : this.withUnits({
                  utrvLike: history,
                  value,
                  utr: universalTracker,
                  className: 'text-ThemeTextMedium',
                  nonNumericClassName: 'text-ThemeTextMedium',
                });
            return (
              <li key={code}>
                <div className='fw-semibold text-ThemeHeadingMedium'>{column?.name ?? code}:</div>
                <div className={'text-ThemeTextMedium'}>{valueDisplay}</div>
              </li>
            );
          }
          return null;
        })
        .filter(Boolean);

      return <ol className='partial-assurance-list pl-0 m-0'>{assuredColumnItems}</ol>;
    }

    if (universalTracker.getValueType() === UtrValueType.Table) {
      const { rows, editRowId } = tableDataToView(valueData);
      const assuredRows = rows.filter(({ id, data }) => {
        return data.every(({ code }) => assuranceFields.some((f) => f.code === code && f.rowIndex === id));
      });

      return (
        <TableInputView
          className='mb-4 mt-2'
          universalTracker={universalTracker}
          disabled={true}
          editRowId={editRowId}
          rowData={assuredRows}
          ignoreType={true}
        />
      );
    }

    return null;
  }

  private static getStatusText({ status, history }: { status: string; history: ValueHistory }) {
    if (status === 'Verified' && isUtrvAssuranceComplete(history.assuranceStatus)) {
      return 'Assured';
    }

    if (isUtrvAssuranceRejected(history.assuranceStatus)) {
      return 'Disputed';
    }

    if (isUtrvAssuranceRestated(history.assuranceStatus)) {
      return 'Restated';
    }

    if (status === 'Updated') {
      return 'Submitted';
    }

    return status;
  }

  /** Main entry point */
  public static getHistoryText({
    className,
    status,
    history,
    username,
    universalTracker,
    unitConfig,
  }: {
    className: string;
    status: string;
    history: ValueHistory;
    username: string;
    universalTracker: UniversalTracker;
    unitConfig?: UnitConfig;
  }) {
    if (isUtrvPartialAssurance(history.assuranceStatus)) {
      const toggleId = `partial-assurance-${history._id}`;
      return (
        <div className='w-100'>
          <div
            id={toggleId}
            className='p-0 text-ThemeTextDark partial-assurance-toggle'
            onClick={(e) => {
              e.currentTarget.classList.toggle('active');
            }}
            data-testid='action-username'
          >
            <span className={className}>
              <strong>Partially assured</strong>
            </span>{' '}
            by <strong>{username}</strong> <i className='fa-light fa-caret-down ml-2 text-ThemeIconSecondary' />
          </div>
          <UncontrolledCollapse toggler={`#${toggleId}`}>
            {this.partialAssuranceProvenance({ history, universalTracker })}
          </UncontrolledCollapse>
        </div>
      );
    }

    const statusText = ValueHistoryText.getStatusText({ status, history });
    const isImported = history.valueData?.isImported === true;
    const statusTextString = (
      <span className={className}>
        <strong>{statusText}</strong>
        {isImported ? ' (via import file)' : ''}{' '}
      </span>
    );
    if (['Created', 'Rejected', 'Verified', 'Assured', 'Disputed'].includes(statusText)) {
      return (
        <div data-testid='action-username'>
          {statusTextString} by <strong>{username}</strong>
        </div>
      );
    }
    return (
      <CollapsibleValueHistoryText
        universalTracker={universalTracker}
        history={history}
        statusTextString={statusTextString}
        username={username}
        unitConfig={unitConfig}
      />
    );
  }

  public static getCompositeHistoryText(
    className: string | undefined,
    statusText: string,
    history: ValueHistory,
    universalTracker: UniversalTracker,
    username: string,
  ) {
    switch (statusText) {
      case 'Rejected':
        return 'There is not enough information to calculate data point';
      case 'Updated':
        return 'Calculation reset due to changes in underlying data points';
      default:
      case 'Verified':
        return ValueHistoryText.getValueText({
          history,
          statusTextString: 'successfully calculated',
          universalTracker,
          className,
          username,
        });
    }
  }
}
