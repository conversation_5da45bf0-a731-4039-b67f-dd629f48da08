import { numberOneWithInput, percentageOneWithInput, numberTwoWithCurrency } from '@fixtures/utr/utr-base-fixtures';
import { utrvNumericValueListOne } from '@fixtures/utr/utr-numericValueList-fixtures';
import { utrvTextValueListOne } from '@fixtures/utr/utr-textValueList-fixtures';
import { utrSingleRowTableOne, utrTableOne, utrTableOneUtrv } from '@fixtures/utr/utr-table-fixtures';
import { utrvValueListOne } from '@fixtures/utr/utr-ValueList-fixtures';
import { partialFieldFixtures } from '@fixtures/assuranceUtrv-fixture';
import UniversalTracker from '@models/UniversalTracker';
import { ValueHistoryText } from './ValueHistoryText';
import { generateValueHistory } from '@fixtures/questions-fixture';
import { UtrvAssuranceStatus } from '@g17eco/types/universalTrackerValue';
import { UtrvStatus } from '@constants/status';
import { render, screen } from '@testing-library/react';
import { reduxFixtureStore } from '@fixtures/redux-store';
import { Provider } from 'react-redux';
import { UnitConfig } from '@models/surveyData';
import { renderWithProviders } from '@fixtures/utils';

const twoWhitespaceRegex = / {2}/g;

const formatText = (str: string | null) => (str ? str.replace(twoWhitespaceRegex, ' ').trim() : str);

// Utr fixtures
const numericUtr = new UniversalTracker(numberOneWithInput.universalTracker);
const percentageUtr = new UniversalTracker(percentageOneWithInput.universalTracker);
const valueListUtr = new UniversalTracker(utrvValueListOne.universalTracker);
const numericValueListUtr = new UniversalTracker(utrvNumericValueListOne.universalTracker);
const textValueListUtr = new UniversalTracker(utrvTextValueListOne.universalTracker);
const singleRowTableUtr = new UniversalTracker(utrSingleRowTableOne);
const multiRowTableUtr = new UniversalTracker(utrTableOne);
const currencyUtr = new UniversalTracker(numberTwoWithCurrency.universalTracker);

describe('ValueHistoryText', () => {
  describe('partialAssuranceProvenance fn', () => {
    it('should return null when history assurance status is not partial', () => {
      const result = ValueHistoryText.partialAssuranceProvenance({
        history: generateValueHistory({
          assuranceStatus: UtrvAssuranceStatus.Created,
          assuranceFields: partialFieldFixtures.numericValueList,
          valueData: utrvNumericValueListOne.valueData,
        }),
        universalTracker: numericValueListUtr,
      });
      expect(result).toBeNull();
    });

    it('should return null when utr is number/percentage/text', () => {
      const result = ValueHistoryText.partialAssuranceProvenance({
        history: generateValueHistory({ assuranceStatus: UtrvAssuranceStatus.Partial }),
        universalTracker: numericUtr,
      });
      expect(result).toBeNull();
    });

    it('should return null when utr is valueList', () => {
      const result = ValueHistoryText.partialAssuranceProvenance({
        history: generateValueHistory({ assuranceStatus: UtrvAssuranceStatus.Partial }),
        universalTracker: valueListUtr,
      });
      expect(result).toBeNull();
    });

    it('should render list items for assured values when utr is numericValueList', () => {
      // numericValueList
      const numericValueListResult = ValueHistoryText.partialAssuranceProvenance({
        history: generateValueHistory({
          assuranceStatus: UtrvAssuranceStatus.Partial,
          assuranceFields: partialFieldFixtures.numericValueList,
          valueData: utrvNumericValueListOne.valueData,
        }),
        universalTracker: numericValueListUtr,
      });
      expect(numericValueListResult?.type).toEqual('ol');
      expect(numericValueListResult?.props?.className).toContain('partial-assurance-list');
      expect(numericValueListResult?.props?.children).toHaveLength(2);
    });

    it('should render list items for assured values when utr is textValueList', () => {
      const textValueListResult = ValueHistoryText.partialAssuranceProvenance({
        history: generateValueHistory({
          assuranceStatus: UtrvAssuranceStatus.Partial,
          assuranceFields: partialFieldFixtures.textValueList,
          valueData: utrvTextValueListOne.valueData,
        }),
        universalTracker: textValueListUtr,
      });
      expect(textValueListResult?.type).toEqual('ol');
      expect(textValueListResult?.props?.className).toContain('partial-assurance-list');
      expect(textValueListResult?.props?.children).toHaveLength(1);
    });

    it('should render list items for assured values when utr is singleRowTable', () => {
      const result = ValueHistoryText.partialAssuranceProvenance({
        history: generateValueHistory({
          assuranceStatus: UtrvAssuranceStatus.Partial,
          assuranceFields: partialFieldFixtures.singleRowTable,
          valueData: utrTableOneUtrv.valueData,
        }),
        universalTracker: singleRowTableUtr,
      });
      expect(result?.type).toEqual('ol');
      expect(result?.props?.className).toContain('partial-assurance-list');
      expect(result?.props?.children).toHaveLength(6);
    });

    it('should render a table for assured values when utr is multiRowTable', () => {
      const result = ValueHistoryText.partialAssuranceProvenance({
        history: generateValueHistory({
          assuranceStatus: UtrvAssuranceStatus.Partial,
          assuranceFields: partialFieldFixtures.multiRowTable,
          valueData: utrTableOneUtrv.valueData,
        }),
        universalTracker: multiRowTableUtr,
      });
      expect(typeof result?.type).toEqual('function');
      expect(typeof result?.props?.disabled).toBeTruthy();
    });
  });

  describe('getHistoryText', () => {
    const className = 'updated';
    const username = 'Full Name';
    describe('Update', () => {
      const statusText = 'Updated';
      it('number question - no unit, no number scale', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          value: 10,
          valueData: { input: { value: 10 } },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );
        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('10');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });

      it('number question - has unit - unit unchanged', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          unit: 'mJ',
          value: 1,
          valueData: {
            input: {
              unit: 'mJ',
              value: 1,
            },
          },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );

        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('1 Megajoule');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });

      it('number question - has unit - unit changed', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          unit: 'mJ',
          value: 1,
          valueData: {
            input: {
              unit: 'J',
              value: 1000000,
            },
          },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );

        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('1,000,000 Joules - Defaults to 1.000 Megajoules');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });

      it('number question - has unit & overridden number scale - unit & number scale unchanged', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          unit: 'mJ',
          numberScale: 'single',
          value: 1,
          valueData: {
            input: {
              unit: 'mJ',
              value: 1,
              numberScale: 'single',
            },
          },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );

        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('1 Megajoule');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });

      it('number question - has unit & overridden number scale - number scale changed', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          unit: 'mJ',
          numberScale: 'single',
          value: 1,
          valueData: {
            input: {
              unit: 'mJ',
              value: 1,
              numberScale: 'thousands',
            },
          },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );

        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('1 Thousand Megajoule - Defaults to 1,000.000 Megajoule');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });

      it('number question - has unit & overridden number scale - both unit & number scale changed', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          unit: 'mJ',
          numberScale: 'single',
          value: 1,
          valueData: {
            input: {
              unit: 'kJ',
              value: 1,
              numberScale: 'thousands',
            },
          },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );

        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('1 Thousand Kilojoule - Defaults to 1.000 Megajoule');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });

      it('percentage question - no overridden number scale - number scale unchanged', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          value: 10,
          valueData: { input: { value: 10 } },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: percentageUtr,
          }),
        );
        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('10 %');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });

      it('percentage question - has overridden number scale', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          value: 10,
          numberScale: 'single',
          valueData: { input: { value: 10, numberScale: 'single' } },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: percentageUtr,
          }),
        );
        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('10 %');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });

      it('percentage question - has overridden number scale - number scale changed', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          value: 10,
          numberScale: 'single',
          valueData: { input: { value: 10, numberScale: 'thousands' } },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: percentageUtr,
          }),
        );
        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('10 Thousands % - Defaults to 10,000.000 %');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });
    });

    describe('Verified', () => {
      const statusText = 'Verified';
      it('should render a collapsible component', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Verified,
          value: 10,
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Verified by Full Name');
      });
    });

    describe('Partially Assured', () => {
      const statusText = 'Verified';
      it('should render a collapsible component', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Verified,
          assuranceStatus: UtrvAssuranceStatus.Partial,
          value: 10,
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Partially assured by Full Name');
      });
    });

    describe('Text Value List', () => {
      const statusText = 'Updated';
      it('should render text value list items', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          valueData: {
            data: {
              option1: 'Value 1',
              option2: 'Value 2',
            },
          },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: textValueListUtr,
          }),
        );
        const historyText = screen.getByTestId('history-text');
        expect(historyText.querySelectorAll('li')).toHaveLength(2);
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });
    });

    describe('Table', () => {
      const statusText = 'Updated';
      const store = reduxFixtureStore();
      it('should render table data', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          valueData: {
            table: [
              [
                { code: 'col1', value: 'Value 1' },
                { code: 'col2', value: 'Value 2' },
              ],
            ],
          },
        });

        render(
          <Provider store={store}>
            {ValueHistoryText.getHistoryText({
              className,
              status: statusText,
              history,
              username,
              universalTracker: singleRowTableUtr,
            })}
          </Provider>,
        );
        expect(screen.getByTestId('action-username')).toBeInTheDocument();
      });
    });

    describe('UnitConfig Parameter Tests', () => {
      const statusText = 'Updated';
      const store = reduxFixtureStore();
      const defaultUnitConfig: UnitConfig = {
        area: 'km2',
        length: 'km',
        time: 'h',
        mass: 'mt',
        volume: 'm3',
        energy: 'MWh',
        currency: 'USD',
        co2Emissions: 'tons/CO2e',
        numberScale: 'millions',
        partsPer: 'ppm',
      };

      describe('CollapsibleValueHistoryText path', () => {
        it('should pass unitConfig to CollapsibleValueHistoryText when status triggers collapsible path', () => {
          const history = generateValueHistory({
            action: UtrvStatus.Updated,
            value: 1000,
            valueData: {
              input: { value: 1000, unit: 'GBP' }
            }
          });

          const result = ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: currencyUtr,
            unitConfig: defaultUnitConfig,
          });

          // Verify that the result is a CollapsibleValueHistoryText component
          expect(result.type.name).toBe('CollapsibleValueHistoryText');
          expect(result.props.unitConfig).toEqual(defaultUnitConfig);
          expect(result.props.universalTracker).toBe(currencyUtr);
          expect(result.props.history).toBe(history);
          expect(result.props.username).toBe(username);
        });

        it('should handle undefined unitConfig in CollapsibleValueHistoryText path', () => {
          const history = generateValueHistory({
            action: UtrvStatus.Updated,
            value: 1000,
            valueData: {
              input: { value: 1000, unit: 'GBP' }
            }
          });

          const result = ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: currencyUtr,
            unitConfig: undefined,
          });

          expect(result.type.name).toBe('CollapsibleValueHistoryText');
          expect(result.props.unitConfig).toBeUndefined();
        });

        it('should pass different currency configurations correctly', () => {
          const eurUnitConfig: UnitConfig = {
            ...defaultUnitConfig,
            currency: 'EUR',
          };

          const history = generateValueHistory({
            action: UtrvStatus.Updated,
            value: 500,
            valueData: {
              input: { value: 500, unit: 'EUR' }
            }
          });

          const result = ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: currencyUtr,
            unitConfig: eurUnitConfig,
          });

          expect(result.props.unitConfig.currency).toBe('EUR');
        });
      });

      describe('Table view with unitConfig', () => {
        it('should pass unitConfig to TableInputView when valueType is table', () => {
          const history = generateValueHistory({
            action: UtrvStatus.Updated,
            valueData: {
              table: [
                [
                  { code: 'currency_col', value: 1000 },
                  { code: 'energy_col', value: 500 },
                ],
              ],
            },
          });

          renderWithProviders(
            ValueHistoryText.getHistoryText({
              className,
              status: statusText,
              history,
              username,
              universalTracker: singleRowTableUtr,
              unitConfig: defaultUnitConfig,
            }),
            { store }
          );

          expect(screen.getByTestId('action-username')).toBeInTheDocument();
          // The table should be rendered with the unitConfig passed through
          // This tests the path where unitConfig is passed to getTableView -> TableInputView
        });

        it('should handle table view without unitConfig', () => {
          const history = generateValueHistory({
            action: UtrvStatus.Updated,
            valueData: {
              table: [
                [
                  { code: 'currency_col', value: 1000 },
                  { code: 'energy_col', value: 500 },
                ],
              ],
            },
          });

          renderWithProviders(
            ValueHistoryText.getHistoryText({
              className,
              status: statusText,
              history,
              username,
              universalTracker: singleRowTableUtr,
              unitConfig: undefined,
            }),
            { store }
          );

          expect(screen.getByTestId('action-username')).toBeInTheDocument();
        });
      });

      describe('Currency-specific edge cases', () => {
        it('should handle partial assurance status with unitConfig', () => {
          const history = generateValueHistory({
            action: UtrvStatus.Updated,
            assuranceStatus: UtrvAssuranceStatus.Partial,
            value: 1000,
            valueData: {
              input: { value: 1000, unit: 'GBP' }
            }
          });

          const result = ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: currencyUtr,
            unitConfig: defaultUnitConfig,
          });

          // For partial assurance, unitConfig should not affect the result structure
          // but the component should still handle it gracefully
          expect(result.type).toBe('div');
          expect(result.props.className).toBe('w-100');
        });

        it('should handle Created status with unitConfig', () => {
          const history = generateValueHistory({
            action: UtrvStatus.Created,
            value: 1000,
            valueData: {
              input: { value: 1000, unit: 'GBP' }
            }
          });

          const result = ValueHistoryText.getHistoryText({
            className,
            status: 'Created',
            history,
            username,
            universalTracker: currencyUtr,
            unitConfig: defaultUnitConfig,
          });

          // Created status should return simple div, not CollapsibleValueHistoryText
          expect(result.type).toBe('div');
          expect(result.props['data-testid']).toBe('action-username');
        });

        it('should handle Verified status with unitConfig', () => {
          const history = generateValueHistory({
            action: UtrvStatus.Verified,
            value: 1000,
            valueData: {
              input: { value: 1000, unit: 'GBP' }
            }
          });

          const result = ValueHistoryText.getHistoryText({
            className,
            status: 'Verified',
            history,
            username,
            universalTracker: currencyUtr,
            unitConfig: defaultUnitConfig,
          });

          // Verified status should return simple div, not CollapsibleValueHistoryText
          expect(result.type).toBe('div');
          expect(result.props['data-testid']).toBe('action-username');
        });

        it('should handle different currency values with unitConfig', () => {
          const sgdUnitConfig: UnitConfig = {
            ...defaultUnitConfig,
            currency: 'SGD',
          };

          const history = generateValueHistory({
            action: UtrvStatus.Updated,
            value: 2000,
            valueData: {
              input: { value: 2000, unit: 'SGD' }
            }
          });

          const result = ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: currencyUtr,
            unitConfig: sgdUnitConfig,
          });

          expect(result.type.name).toBe('CollapsibleValueHistoryText');
          expect(result.props.unitConfig.currency).toBe('SGD');
        });

        it('should handle empty unitConfig object', () => {
          const emptyUnitConfig = {} as UnitConfig;

          const history = generateValueHistory({
            action: UtrvStatus.Updated,
            value: 1000,
            valueData: {
              input: { value: 1000, unit: 'GBP' }
            }
          });

          const result = ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: currencyUtr,
            unitConfig: emptyUnitConfig,
          });

          expect(result.type.name).toBe('CollapsibleValueHistoryText');
          expect(result.props.unitConfig).toEqual(emptyUnitConfig);
        });

        it('should handle non-currency universal trackers with unitConfig', () => {
          const history = generateValueHistory({
            action: UtrvStatus.Updated,
            value: 100,
            valueData: {
              input: { value: 100, unit: 'mJ' }
            }
          });

          const result = ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr, // Non-currency tracker
            unitConfig: defaultUnitConfig,
          });

          // Should still pass unitConfig even for non-currency trackers
          expect(result.type.name).toBe('CollapsibleValueHistoryText');
          expect(result.props.unitConfig).toEqual(defaultUnitConfig);
          expect(result.props.universalTracker).toBe(numericUtr);
        });

        it('should handle percentage tracker with unitConfig', () => {
          const history = generateValueHistory({
            action: UtrvStatus.Updated,
            value: 50,
            valueData: {
              input: { value: 50 }
            }
          });

          const result = ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: percentageUtr,
            unitConfig: defaultUnitConfig,
          });

          expect(result.type.name).toBe('CollapsibleValueHistoryText');
          expect(result.props.unitConfig).toEqual(defaultUnitConfig);
          expect(result.props.universalTracker).toBe(percentageUtr);
        });

        it('should verify unitConfig is not used in non-collapsible status paths', () => {
          const history = generateValueHistory({
            action: UtrvStatus.Rejected,
            value: 1000,
            valueData: {
              input: { value: 1000, unit: 'GBP' }
            }
          });

          const result = ValueHistoryText.getHistoryText({
            className,
            status: 'Rejected',
            history,
            username,
            universalTracker: currencyUtr,
            unitConfig: defaultUnitConfig,
          });

          // Rejected status returns simple div, unitConfig should not be used
          expect(result.type).toBe('div');
          expect(result.props['data-testid']).toBe('action-username');
          expect(result.props.unitConfig).toBeUndefined();
        });

        it('should verify unitConfig is not used in Assured status paths', () => {
          const history = generateValueHistory({
            action: UtrvStatus.Verified,
            value: 1000,
            valueData: {
              input: { value: 1000, unit: 'GBP' }
            }
          });

          const result = ValueHistoryText.getHistoryText({
            className,
            status: 'Assured',
            history,
            username,
            universalTracker: currencyUtr,
            unitConfig: defaultUnitConfig,
          });

          // Assured status returns simple div, unitConfig should not be used
          expect(result.type).toBe('div');
          expect(result.props['data-testid']).toBe('action-username');
          expect(result.props.unitConfig).toBeUndefined();
        });
      });
    });

    describe('Value List Multi', () => {
      const statusText = 'Updated';
      it('should render value list items', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          valueData: {
            data: ['option1', 'option2'],
          },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: valueListUtr,
          }),
        );
        const historyText = screen.getByTestId('history-text');
        expect(historyText.querySelectorAll('li')).toHaveLength(2);
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });
    });

    describe('Imported Data', () => {
      const statusText = 'Updated';
      it('should show imported data indicator', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          value: 10,
          valueData: {
            input: { value: 10 },
            isImported: true,
          },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );
        expect(screen.getByText('(via import file)')).toBeInTheDocument();
      });
    });

    describe('Rejected Status', () => {
      const statusText = 'Rejected';
      it('should render rejected status', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Rejected,
          value: 10,
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Rejected by Full Name');
      });
    });

    describe('Created Status', () => {
      const statusText = 'Created';
      it('should render created status', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Created,
          value: 10,
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Created by Full Name');
      });
    });

    
  });
});
