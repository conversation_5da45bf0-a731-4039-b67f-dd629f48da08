import { numberOneWithInput, percentageOneWithInput } from '@fixtures/utr/utr-base-fixtures';
import { utrvNumericValueListOne } from '@fixtures/utr/utr-numericValueList-fixtures';
import { utrvTextValueListOne } from '@fixtures/utr/utr-textValueList-fixtures';
import { utrSingleRowTableOne, utrTableOne, utrTableOneUtrv } from '@fixtures/utr/utr-table-fixtures';
import { utrvValueListOne } from '@fixtures/utr/utr-ValueList-fixtures';
import { partialFieldFixtures } from '@fixtures/assuranceUtrv-fixture';
import UniversalTracker from '@models/UniversalTracker';
import { ValueHistoryText } from './ValueHistoryText';
import { generateValueHistory } from '@fixtures/questions-fixture';
import { UtrvAssuranceStatus } from '@g17eco/types/universalTrackerValue';
import { UtrvStatus } from '@constants/status';
import { render, screen } from '@testing-library/react';
import { reduxFixtureStore } from '@fixtures/redux-store';
import { Provider } from 'react-redux';

const twoWhitespaceRegex = / {2}/g;

const formatText = (str: string | null) => (str ? str.replace(twoWhitespaceRegex, ' ').trim() : str);

// Utr fixtures
const numericUtr = new UniversalTracker(numberOneWithInput.universalTracker);
const percentageUtr = new UniversalTracker(percentageOneWithInput.universalTracker);
const valueListUtr = new UniversalTracker(utrvValueListOne.universalTracker);
const numericValueListUtr = new UniversalTracker(utrvNumericValueListOne.universalTracker);
const textValueListUtr = new UniversalTracker(utrvTextValueListOne.universalTracker);
const singleRowTableUtr = new UniversalTracker(utrSingleRowTableOne);
const multiRowTableUtr = new UniversalTracker(utrTableOne);

describe('ValueHistoryText', () => {
  describe('partialAssuranceProvenance fn', () => {
    it('should return null when history assurance status is not partial', () => {
      const result = ValueHistoryText.partialAssuranceProvenance({
        history: generateValueHistory({
          assuranceStatus: UtrvAssuranceStatus.Created,
          assuranceFields: partialFieldFixtures.numericValueList,
          valueData: utrvNumericValueListOne.valueData,
        }),
        universalTracker: numericValueListUtr,
      });
      expect(result).toBeNull();
    });

    it('should return null when utr is number/percentage/text', () => {
      const result = ValueHistoryText.partialAssuranceProvenance({
        history: generateValueHistory({ assuranceStatus: UtrvAssuranceStatus.Partial }),
        universalTracker: numericUtr,
      });
      expect(result).toBeNull();
    });

    it('should return null when utr is valueList', () => {
      const result = ValueHistoryText.partialAssuranceProvenance({
        history: generateValueHistory({ assuranceStatus: UtrvAssuranceStatus.Partial }),
        universalTracker: valueListUtr,
      });
      expect(result).toBeNull();
    });

    it('should render list items for assured values when utr is numericValueList', () => {
      // numericValueList
      const numbericValueListResult = ValueHistoryText.partialAssuranceProvenance({
        history: generateValueHistory({
          assuranceStatus: UtrvAssuranceStatus.Partial,
          assuranceFields: partialFieldFixtures.numericValueList,
          valueData: utrvNumericValueListOne.valueData,
        }),
        universalTracker: numericValueListUtr,
      });
      expect(numbericValueListResult?.type).toEqual('ol');
      expect(numbericValueListResult?.props?.className).toContain('partial-assurance-list');
      expect(numbericValueListResult?.props?.children).toHaveLength(2);
    });

    it('should render list items for assured values when utr is textValueList', () => {
      const textValueListResult = ValueHistoryText.partialAssuranceProvenance({
        history: generateValueHistory({
          assuranceStatus: UtrvAssuranceStatus.Partial,
          assuranceFields: partialFieldFixtures.textValueList,
          valueData: utrvTextValueListOne.valueData,
        }),
        universalTracker: textValueListUtr,
      });
      expect(textValueListResult?.type).toEqual('ol');
      expect(textValueListResult?.props?.className).toContain('partial-assurance-list');
      expect(textValueListResult?.props?.children).toHaveLength(1);
    });

    it('should render list items for assured values when utr is singleRowTable', () => {
      const result = ValueHistoryText.partialAssuranceProvenance({
        history: generateValueHistory({
          assuranceStatus: UtrvAssuranceStatus.Partial,
          assuranceFields: partialFieldFixtures.singleRowTable,
          valueData: utrTableOneUtrv.valueData,
        }),
        universalTracker: singleRowTableUtr,
      });
      expect(result?.type).toEqual('ol');
      expect(result?.props?.className).toContain('partial-assurance-list');
      expect(result?.props?.children).toHaveLength(6);
    });

    it('should render a table for assured values when utr is multiRowTable', () => {
      const result = ValueHistoryText.partialAssuranceProvenance({
        history: generateValueHistory({
          assuranceStatus: UtrvAssuranceStatus.Partial,
          assuranceFields: partialFieldFixtures.multiRowTable,
          valueData: utrTableOneUtrv.valueData,
        }),
        universalTracker: multiRowTableUtr,
      });
      expect(typeof result?.type).toEqual('function');
      expect(typeof result?.props?.disabled).toBeTruthy();
    });
  });

  describe('getHistoryText', () => {
    const className = 'updated';
    const username = 'Full Name';
    describe('Update', () => {
      const statusText = 'Updated';
      it('number question - no unit, no number scale', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          value: 10,
          valueData: { input: { value: 10 } },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );
        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('10');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });

      it('number question - has unit - unit unchanged', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          unit: 'mJ',
          value: 1,
          valueData: {
            input: {
              unit: 'mJ',
              value: 1,
            },
          },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );

        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('1 Megajoule');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });

      it('number question - has unit - unit changed', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          unit: 'mJ',
          value: 1,
          valueData: {
            input: {
              unit: 'J',
              value: 1000000,
            },
          },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );

        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('1,000,000 Joules - Defaults to 1.000 Megajoules');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });

      it('number question - has unit & overridden number scale - unit & number scale unchanged', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          unit: 'mJ',
          numberScale: 'single',
          value: 1,
          valueData: {
            input: {
              unit: 'mJ',
              value: 1,
              numberScale: 'single',
            },
          },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );

        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('1 Megajoule');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });

      it('number question - has unit & overridden number scale - number scale changed', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          unit: 'mJ',
          numberScale: 'single',
          value: 1,
          valueData: {
            input: {
              unit: 'mJ',
              value: 1,
              numberScale: 'thousands',
            },
          },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );

        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('1 Thousand Megajoule - Defaults to 1,000.000 Megajoule');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });

      it('number question - has unit & overridden number scale - both unit & number scale changed', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          unit: 'mJ',
          numberScale: 'single',
          value: 1,
          valueData: {
            input: {
              unit: 'kJ',
              value: 1,
              numberScale: 'thousands',
            },
          },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );

        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('1 Thousand Kilojoule - Defaults to 1.000 Megajoule');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });

      it('percentage question - no overridden number scale - number scale unchanged', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          value: 10,
          valueData: { input: { value: 10 } },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: percentageUtr,
          }),
        );
        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('10 %');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });

      it('percentage question - has overridden number scale', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          value: 10,
          numberScale: 'single',
          valueData: { input: { value: 10, numberScale: 'single' } },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: percentageUtr,
          }),
        );
        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('10 %');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });

      it('percentage question - has overridden number scale - number scale changed', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          value: 10,
          numberScale: 'single',
          valueData: { input: { value: 10, numberScale: 'thousands' } },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: percentageUtr,
          }),
        );
        const historyText = screen.getByTestId('history-text');
        expect(formatText(historyText.textContent)).toEqual('10 Thousands % - Defaults to 10,000.000 %');
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });
    });

    describe('Verified', () => {
      const statusText = 'Verified';
      it('should render a collapsible component', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Verified,
          value: 10,
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Verified by Full Name');
      });
    });

    describe('Partially Assured', () => {
      const statusText = 'Verified';
      it('should render a collapsible component', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Verified,
          assuranceStatus: UtrvAssuranceStatus.Partial,
          value: 10,
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Partially assured by Full Name');
      });
    });

    describe('Text Value List', () => {
      const statusText = 'Updated';
      it('should render text value list items', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          valueData: {
            data: {
              option1: 'Value 1',
              option2: 'Value 2',
            },
          },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: textValueListUtr,
          }),
        );
        const historyText = screen.getByTestId('history-text');
        expect(historyText.querySelectorAll('li')).toHaveLength(2);
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });
    });

    describe('Table', () => {
      const statusText = 'Updated';
      const store = reduxFixtureStore();
      it('should render table data', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          valueData: {
            table: [
              [
                { code: 'col1', value: 'Value 1' },
                { code: 'col2', value: 'Value 2' },
              ],
            ],
          },
        });

        render(
          <Provider store={store}>
            {ValueHistoryText.getHistoryText({
              className,
              status: statusText,
              history,
              username,
              universalTracker: singleRowTableUtr,
            })}
          </Provider>,
        );
        expect(screen.getByTestId('action-username')).toBeInTheDocument();
      });
    });

    describe('Value List Multi', () => {
      const statusText = 'Updated';
      it('should render value list items', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          valueData: {
            data: ['option1', 'option2'],
          },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: valueListUtr,
          }),
        );
        const historyText = screen.getByTestId('history-text');
        expect(historyText.querySelectorAll('li')).toHaveLength(2);
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Submitted by Full Name');
      });
    });

    describe('Imported Data', () => {
      const statusText = 'Updated';
      it('should show imported data indicator', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Updated,
          value: 10,
          valueData: {
            input: { value: 10 },
            isImported: true,
          },
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );
        expect(screen.getByText('(via import file)')).toBeInTheDocument();
      });
    });

    describe('Rejected Status', () => {
      const statusText = 'Rejected';
      it('should render rejected status', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Rejected,
          value: 10,
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Rejected by Full Name');
      });
    });

    describe('Created Status', () => {
      const statusText = 'Created';
      it('should render created status', () => {
        const history = generateValueHistory({
          action: UtrvStatus.Created,
          value: 10,
        });

        render(
          ValueHistoryText.getHistoryText({
            className,
            status: statusText,
            history,
            username,
            universalTracker: numericUtr,
          }),
        );
        expect(formatText(screen.getByTestId('action-username').textContent)).toEqual('Created by Full Name');
      });
    });

    
  });
});
