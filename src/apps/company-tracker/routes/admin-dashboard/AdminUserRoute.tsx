/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Loader } from '@g17eco/atoms/loader';
import Dashboard, {
  DashboardColumn,
  DashboardRow,
  DashboardSection,
  DashboardSectionTitle,
} from '@g17eco/molecules/dashboard';
import { DATE, formatDate } from '@utils/date';
import { useParams } from 'react-router';
import { AdminBreadcrumbs } from '@routes/admin-dashboard/AdminBreadcrumbs';
import { Button } from 'reactstrap';
import { ColumnDef, Table } from '@g17eco/molecules/table';
import { BasicAlert } from '@g17eco/molecules/alert';
import { ColumnTitle } from '@g17eco/molecules/survey-question-list';
import { Divider } from '@g17eco/molecules/tracking-list';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { SurveySummary } from '@g17eco/types/survey';
import { getPeriodName } from '@utils/universalTracker';
import { Link } from 'react-router-dom';
import { getFullName } from '@utils/user';
import { useGetStatsQuery, useGetUserSurveyDetailsQuery, useGetUserSurveyStatsQuery } from '@api/initiative-stats';
import { UserMessageModal } from '@components/message-modal/MessageModal';
import { generateUrl } from '@routes/util';
import { ROUTES } from '@constants/routes';
import G17Client from '@services/G17Client';
import SurveyQuestionList from '@components/survey-question-list/survey-question-list';
import { ReportSwitcherContainer } from '@components/initiative/ReportSwitcherContainer';
import { ColumnValue } from '@components/survey-question-list/partials/ColumnValue';
import { ColumnStatusIcon } from '@components/survey-question-list/partials/ColumnStatusIcon';
import { BulkActionToolbar } from '@components/survey-question-list/partials/BulkActionToolbar';
import { getSurveyName } from '@utils/surveyData';
import { SurveyQuestionListToolbar } from '@components/survey/survey-question-list-toolbar';
import { ScopeViewDropdown } from '@components/survey/survey-question-list-toolbar/partials/ScopeViewDropdown';
import { getAdminRouteHiddenOptions, useSurveyGroups } from '@utils/survey';
import { useAppSelector } from '@reducers/index';
import { ExpandAllToggle } from '@components/survey/survey-question-list-toolbar/partials/ExpandAllToggle';
import { SelectAllToggle } from '@components/survey/survey-question-list-toolbar/partials/SelectAllToggle';
import { naturalSort } from '@utils/index';
import { InitiativePermissions } from '@services/permissions/InitiativePermissions';
import { Menu } from '@components/menu/Menu';
import { QUESTION, SURVEY } from '@constants/terminology';
import { ColumnBookmark } from '@components/survey-question-list/partials/ColumnBookmark';
import { isRootOrg } from '@selectors/initiative';
import { getRootInitiativeMap } from '@features/question-configuration';
import { UserPermissions } from '@constants/users';
import { getRoleName, UserRoles } from '@constants/user';
import { useGetInitiativeUniversalTrackersQuery } from '@api/initiative-universal-trackers';
import { skipToken } from '@reduxjs/toolkit/query';
import QuestionManagementContainer from '../../components/admin-dashboard/questions/QuestionManagementContainer';

interface RouteParams {
  initiativeId: string;
  userId: string;
}

interface SurveyStats
  extends Pick<
    SurveySummary,
    '_id' | 'status' | 'name' | 'period' | 'effectiveDate' | 'initiativeId' | 'type' | 'completedDate'
  > {
  lastUpdated: string;
  initiativeName?: string;
}

const getDisplayRoles = ({ permissions, initiativeId }: { permissions?: UserPermissions[]; initiativeId: string }) => {
  if (!permissions) {
    return [];
  }

  const initiativePermissions = permissions.find((p) => p.initiativeId === initiativeId);
  if (!initiativePermissions) {
    return [];
  }
  const roles = Object.values(UserRoles);
  return initiativePermissions.permissions
    .reduce<string[]>((acc, permission) => (roles.includes(permission) ? [...acc, getRoleName(permission)] : acc), [])
    .join(', ');
};

export const AdminUserRoute = () => {
  const { initiativeId, userId } = useParams<RouteParams>();

  const [isError, setIsError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const [filter, setFilter] = useState<'pending' | 'completed'>('pending');
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [selectedSurvey, setSelectedSurvey] = useState<Pick<SurveyStats, '_id' | 'completedDate'> | undefined>(
    undefined
  );

  const isCompleteFilter = filter === 'completed';
  const { data = [] } = useGetInitiativeUniversalTrackersQuery(initiativeId ?? skipToken);

  const rootInitiativeUtrMap = getRootInitiativeMap(data);

  const surveyDetails = useGetUserSurveyDetailsQuery(
    {
      initiativeId,
      surveyId: selectedSurvey?._id ?? '',
      userId,
    },
    {
      skip: !selectedSurvey?._id,
    }
  );
  const surveyStats = useGetUserSurveyStatsQuery({
    initiativeId,
    userId,
    isCompleted: isCompleteFilter,
  });

  const user = surveyStats.data?.user;

  const canContributeInitiative = user ? InitiativePermissions.canContribute(user, initiativeId) : false;
  const canVerifyInitiative = user ? InitiativePermissions.canVerify(user, initiativeId) : false;
  const fetchInitiativeSurveys = canContributeInitiative || canVerifyInitiative;

  const totalStats = useGetStatsQuery({ initiativeId }, { skip: !fetchInitiativeSurveys });

  const updateStates = useCallback(
    (statsIsLoading: boolean, statsIsError: boolean) => {
      if (isError !== statsIsError) {
        setIsError(statsIsError);
      }
      if (isLoading !== statsIsLoading) {
        setIsLoading(statsIsLoading);
      }
    },
    [isError, isLoading]
  );

  const surveyData: SurveyStats[] = useMemo(() => {
    if (fetchInitiativeSurveys) {
      updateStates(totalStats.isLoading, totalStats.isError);
      if (!totalStats.isSuccess) {
        return [];
      }
      return totalStats.data.reduce((acc, { surveys }) => {
        const filteredSurveys = surveys.filter((s) => (isCompleteFilter ? s.completedDate : !s.completedDate));
        if (filteredSurveys.length > 0) {
          return [...acc, ...filteredSurveys];
        }
        return acc;
      }, [] as SurveyStats[]);
    }
    updateStates(surveyStats.isLoading, surveyStats.isError);
    if (!surveyStats.isSuccess) {
      return [];
    }
    return surveyStats.data.surveys;
  }, [fetchInitiativeSurveys, updateStates, totalStats, surveyStats, isCompleteFilter]);

  useEffect(() => {
    const firstSurvey = surveyData[0];
    if (!firstSurvey) {
      return;
    }
    setSelectedSurvey({ _id: firstSurvey._id, completedDate: firstSurvey.completedDate });
  }, [surveyData]);

  const isRootOrganization = useAppSelector(isRootOrg);
  const overviewMode = useAppSelector((state) => state.surveySettings.overviewMode);
  const surveyGroups = useSurveyGroups(overviewMode, surveyDetails.data);

  const breadcrumbs = [
    { label: 'Admin Dashboard', url: generateUrl(ROUTES.ADMIN_DASHBOARD, { initiativeId }) },
    { label: 'User View' },
  ];

  if (isLoading || !user) {
    return <Loader />;
  }

  if (isError) {
    return (
      <div className='admin-dashboard-overview'>
        <Dashboard>
          <DashboardRow>
            <AdminBreadcrumbs breadcrumbs={breadcrumbs} initiativeId={initiativeId} />
          </DashboardRow>
        </Dashboard>

        <Dashboard className='admin-dashboard'>
          <DashboardRow title={'Admin dashboard'}>
            <DashboardColumn flexBasisPc={'100%'}>
              <BasicAlert type={'danger'}>
                {surveyStats.error?.message ?? 'There was a problem loading user information'}
              </BasicAlert>
            </DashboardColumn>
          </DashboardRow>
        </Dashboard>
      </div>
    );
  }

  const handleReload = () => surveyDetails.refetch();
  const handleChangeSurvey = (newSurveyId: string) => {
    const selectedSurvey = surveyData.find((survey) => survey._id === newSurveyId);
    if (!selectedSurvey) {
      return;
    }
    setSelectedSurvey({ _id: selectedSurvey._id, completedDate: selectedSurvey.completedDate });
  };

  const downloadXlsx = async () => {
    return G17Client.downloadUserStatsView(initiativeId, userId).catch((e) => console.log(e));
  };
  const columns: ColumnDef<SurveyStats>[] = [
    {
      header: 'Type',
      accessorFn: (row) => getPeriodName(row.period, false),
    },
    {
      header: 'Up to',
      accessorFn: (row) => formatDate(row.effectiveDate, DATE.MONTH_YEAR_SHORT),
      sortingFn: (a, b) => naturalSort(a.original.effectiveDate, b.original.effectiveDate),
    },
    {
      id: 'name',
      header: 'Title',
      meta: {
        cellProps: {
          style: {
            maxWidth: 250,
          },
        },
      },
      cell: ({ row }) => {
        const stats = row.original;
        const url = generateUrl(ROUTES.COMPANY_TRACKER_SURVEY, {
          initiativeId: stats.initiativeId,
          surveyId: stats._id,
          page: 'overview',
        });
        return (
          <Link to={`${url}?filterByDelegationStatus[]=${user._id}`}>
            <span className={'text-truncate'} style={{ display: 'block' }}>
              {stats.name ?? stats.initiativeName ?? '-'}
            </span>
          </Link>
        );
      },
    },
    {
      header: 'Updated',
      accessorFn: (row: SurveyStats) => formatDate(row.lastUpdated, DATE.HUMANIZE),
      sortingFn: (a, b) => naturalSort(a.original.lastUpdated, b.original.lastUpdated)
    },
    {
      header: () => <i className='fa fa-user-clock text-secondary mr-2' />,
      accessorKey: 'status.created',
      meta: {
        cellProps: { style: { width: 20 } },
      },
    },
    {
      header: () => <i className='fa fa-user-edit text-primary mr-2' />,
      accessorKey: 'status.updated',
      meta: {
        cellProps: { style: { width: 20 } },
      },
    },
    {
      header: () => <i className='fa fa-user-times text-danger mr-2' />,
      accessorKey: 'status.rejected',
      meta: {
        cellProps: { style: { width: 20 } },
      },
    },
    {
      header: () => <i className='fa fa-user-check text-success mr-2' />,
      accessorKey: 'status.verified',
      meta: {
        cellProps: { style: { width: 20 } },
      },
    },
  ];

  return (
    <div className='admin-dashboard-overview'>
      <Dashboard className='admin-dashboard'>
        <DashboardRow>
          <AdminBreadcrumbs breadcrumbs={breadcrumbs} initiativeId={initiativeId} />
        </DashboardRow>

        <DashboardRow
          className={'header-row'}
          title={
            <>
              Admin dashboard: {getFullName(user)}
              <Button color='link' className='ml-2'>
                <i className='fa-solid fa-file-excel' onClick={() => downloadXlsx()} />
              </Button>
            </>
          }
          buttons={[
            <Button key='message' color={'secondary'} outline onClick={() => setShowMessageModal(true)}>
              Message
            </Button>,
          ]}
        >
          <div className='ml-2 mb-3 header-subtitle'>
            <span className={'pr-1 mr-1 header-subtitle-prefix'}>{user.email}</span>
            <span className={'pr-1 mr-1 header-subtitle-prefix'}>
              {getDisplayRoles({ initiativeId, permissions: user.permissions })}
            </span>
            <span>Last online {formatDate(user.lastLogin ?? '', DATE.HUMANIZE) ?? '-'}</span>
          </div>
        </DashboardRow>

        <DashboardSectionTitle title={SURVEY.CAPITALIZED_PLURAL} />

        <DashboardSection className={'mb-5'}>
          <Menu
            grouped
            items={[
              {
                label: 'In Progress',
                onClick: () => setFilter('pending'),
                active: !isCompleteFilter,
              },
              {
                label: 'Completed',
                onClick: () => setFilter('completed'),
                active: isCompleteFilter,
              },
            ]}
          />

          {surveyStats.isLoading || surveyStats.isFetching ? (
            <Loader relative />
          ) : (
            <Table
              columns={columns}
              data={surveyData}
              sortBy={[{ id: 'date', desc: false }]}
              noData={<BasicAlert type='info'>The selected user has no {SURVEY.PLURAL} assigned</BasicAlert>}
            />
          )}
        </DashboardSection>

        {surveyData.length > 0 ? (
          <>
            <DashboardSectionTitle
              title={`${SURVEY.CAPITALIZED_SINGULAR} ${QUESTION.PLURAL}`}
              buttons={[
                <ReportSwitcherContainer
                  key='report-switcher'
                  surveyList={surveyData.map((r) => ({
                    ...r,
                    name: getSurveyName({ ...r, initiatives: [{ name: r.initiativeName }] }),
                  }))}
                  initiativeId={initiativeId}
                  selectedSurveyId={selectedSurvey?._id}
                  onChange={handleChangeSurvey}
                  defaultToCurrent={false}
                />,
              ]}
            />

            <DashboardSection className={'mb-5'}>
              <LoadingPlaceholder isLoading={surveyDetails.isFetching} height={300}>
                {selectedSurvey?._id && surveyDetails.isSuccess ? (
                  <QuestionManagementContainer>
                    <SurveyQuestionList
                      surveyId={selectedSurvey._id}
                      initiativeId={initiativeId}
                      surveyGroups={surveyGroups}
                      bulkActionToolbar={
                        selectedSurvey.completedDate
                          ? undefined
                          : (props) =>
                              surveyDetails.currentData ? (
                                <BulkActionToolbar
                                  {...props}
                                  rootInitiativeUtrMap={rootInitiativeUtrMap}
                                  hiddenOptions={getAdminRouteHiddenOptions(isRootOrganization)}
                                  surveyData={surveyDetails.currentData}
                                  handleReload={handleReload}
                                />
                              ) : undefined
                      }
                      columns={[
                        ColumnBookmark,
                        ColumnTitle,
                        Divider,
                        (props) => <ColumnValue {...props} unitConfig={surveyDetails.data.unitConfig} />,
                        Divider,
                        ColumnStatusIcon,
                      ]}
                      toolbar={(props) =>
                        selectedSurvey._id && surveyDetails.isSuccess ? (
                          <SurveyQuestionListToolbar
                            {...props}
                            surveyScope={surveyDetails.data.scope}
                            components={{
                              top: [ScopeViewDropdown],
                              bottom: [ExpandAllToggle, ...(!selectedSurvey.completedDate ? [SelectAllToggle] : [])],
                            }}
                          />
                        ) : null
                      }
                    />
                  </QuestionManagementContainer>
                ) : (
                  <BasicAlert type='info'>Please select a {SURVEY.SINGULAR}</BasicAlert>
                )}
              </LoadingPlaceholder>
            </DashboardSection>
          </>
        ) : null}
      </Dashboard>
      {showMessageModal ? (
        <UserMessageModal users={[user]} userIds={[user._id]} toggle={() => setShowMessageModal(!showMessageModal)} />
      ) : null}
    </div>
  );
};
