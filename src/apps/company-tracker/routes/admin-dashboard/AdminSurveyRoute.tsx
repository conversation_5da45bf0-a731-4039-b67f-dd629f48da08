/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { ReactNode, useMemo, useState } from 'react';
import { Button } from 'reactstrap';
import SurveyQuestionList from '@components/survey-question-list/survey-question-list';
import { useAppSelector } from '@reducers/index';
import Dashboard, { DashboardRow, DashboardSection, DashboardSectionTitle } from '@g17eco/molecules/dashboard';
import { useHistory, useParams } from 'react-router';
import { AdminBreadcrumbs } from '@routes/admin-dashboard/AdminBreadcrumbs';
import { Loader } from '@g17eco/atoms/loader';
import { generateUrl } from '@routes/util';
import { ROUTES } from '@constants/routes';
import { CompleteButton } from '@components/survey/button/CompleteButton';
import { ProgressRow } from '../../components/admin-dashboard/ProgressRow';
import { toPercentageProgress } from '../../components/admin-dashboard/util';
import { SurveyMessageModal } from '@components/message-modal/MessageModal';
import { getCurrentUser, isUserManagerByInitiativeId } from '@selectors/user';
import { SurveyQuestionListToolbar, ToolbarComponentCommonProps } from '@components/survey/survey-question-list-toolbar';
import { ScopeViewDropdown } from '@components/survey/survey-question-list-toolbar/partials/ScopeViewDropdown';
import { DelegationViewToggle } from '@components/survey/survey-question-list-toolbar/partials/DelegationViewToggle';
import { getAdminRouteHiddenOptions, getFormattedSurveyDate, getSurveyQuestionProgress, useSurveyGroups } from '@utils/survey';
import { ExpandAllToggle } from '@components/survey/survey-question-list-toolbar/partials/ExpandAllToggle';
import { useGetSurveyUsersQuery, useGetUserSurveyDetailsQuery } from '@api/initiative-stats';
import { ColumnTitle } from '@g17eco/molecules/survey-question-list';
import { Divider } from '@g17eco/molecules/tracking-list';
import { FilterToggle } from '@g17eco/molecules/filter-toggle';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { ColumnValue } from '@components/survey-question-list/partials/ColumnValue';
import { ColumnStatusIcon } from '@components/survey-question-list/partials/ColumnStatusIcon';
import { ColumnContributors } from '@components/survey-question-list/partials/ColumnContributors';
import { ColumnVerifiers } from '@components/survey-question-list/partials/ColumnVerifiers';
import G17Client from '@services/G17Client';
import { BulkActionToolbar } from '@components/survey-question-list/partials/BulkActionToolbar';
import { ColumnOptionsMenu } from '@components/survey-question-list/partials/ColumnOptionsMenu';
import { SearchQuestions } from '@components/survey/survey-question-list-toolbar/partials/SearchQuestions';
import { useFilterQuestions } from '../../hooks/useFilterQuestions';
import { getRootOrg, isRootOrg } from '@selectors/initiative';
import { QUESTION, SURVEY } from '@constants/terminology';
import { ColumnBookmark } from '@components/survey-question-list/partials/ColumnBookmark';
import { skipToken } from '@reduxjs/toolkit/query';
import { QueryError } from '@g17eco/molecules/query/QueryError';
import { getRootInitiativeMap } from '@features/question-configuration';
import { useGetInitiativeUniversalTrackersQuery } from '@api/initiative-universal-trackers';
import QuestionManagementContainer from '../../components/admin-dashboard/questions/QuestionManagementContainer';
import { getBranchInitiativeNameText } from '@routes/initiative-structure/utils';
import { getInitiativeTree } from '@selectors/initiativeTree';

export interface AdminSurveyParams {
  initiativeId: string;
  surveyId: string;
}

interface DelegationActionsToolbarProps extends ToolbarComponentCommonProps {
  setDelegationView: (view: boolean) => void;
  delegationView: boolean;
  handleDownloadSurvey: () => void;
  setShowMessageModal: (show: boolean) => void;
}

const DelegateActionsToolbar = ({
  setDelegationView,
  delegationView,
  handleDownloadSurvey,
  setShowMessageModal,
  ...props
}: DelegationActionsToolbarProps) => (
  <div className='w-100 d-flex flex-wrap gap-3 align-items-center justify-content-end pb-2'>
    <DelegationViewToggle {...props} handleCheckbox={setDelegationView} delegationView={delegationView} />
    <Button color='secondary' className='d-flex gap-2' onClick={handleDownloadSurvey}>
      <i className='fal fa-file-excel' />
      Download Delegation
    </Button>
    <Button key='message' color='secondary' onClick={() => setShowMessageModal(true)}>
      Message Users
    </Button>
  </div>
);

const SearchAndScopeToolbar = (props: ToolbarComponentCommonProps) => (
  <div className='flex-grow-1 d-flex flex-wrap gap-3 align-items-center justify-content-between'>
    <SearchQuestions {...props} />
    <ScopeViewDropdown {...props} />
  </div>
);

type TitleRowProps = {
  label: string;
  children: ReactNode;
  classNames?: Record<string, string | undefined>;
};

const TitleRow = ({ label, children, classNames = {} }: TitleRowProps) => (
  <div className={`d-flex align-items-center gap-4 ${classNames.wrapper ?? ''}`}>
    <span className={`fs-5 fw-bold ${classNames.label ?? ''}`}>{label}</span>
    {children ? children : null}
  </div>
);

export const AdminSurveyRoute = () => {
  const { initiativeId, surveyId } = useParams<AdminSurveyParams>();

  const [showMessageModal, setShowMessageModal] = useState(false);
  const [delegationView, setDelegationView] = useState(true);

  const history = useHistory()
  const currentUser = useAppSelector(getCurrentUser);
  const isUserAdmin = useAppSelector((state) => isUserManagerByInitiativeId(state, initiativeId));

  const { data = [] } = useGetInitiativeUniversalTrackersQuery(initiativeId ?? skipToken);

  const rootInitiativeUtrMap = getRootInitiativeMap(data);

  const surveyDetails = useGetUserSurveyDetailsQuery(
    {
      initiativeId,
      surveyId,
      userId: currentUser?._id ?? '',
    },
    {
      skip: !surveyId || !currentUser,
    }
  );

  const surveyUsers = useGetSurveyUsersQuery({ initiativeId, surveyId });

  const isSurveyLoaded = surveyDetails.isSuccess;
  const overviewMode = useAppSelector((state) => state.surveySettings.overviewMode);
  const isRootOrganization = useAppSelector(isRootOrg);
  const rootInitiative = useAppSelector(getRootOrg);
  const defaultTree = useAppSelector(getInitiativeTree);

  const surveyGroups = useSurveyGroups(overviewMode, surveyDetails.data);
  const progressStats = useMemo(() => getSurveyQuestionProgress(surveyGroups), [surveyGroups]);

  const { filters, changeFilter, disabledUTRs } = useFilterQuestions({
    survey: surveyDetails.data,
    surveyGroups,
  });

  if (!isSurveyLoaded || surveyUsers.isLoading) {
    return <Loader />;
  }

  if (!currentUser) {
    return (
      <DashboardSection
        icon='fa-exclamation-triangle'
        title='Permission Denied'
        subtitle='You do not have permission to access this'
      />
    );
  }

  const users = surveyUsers.data ?? [];
  const surveyData = surveyDetails.data;
  const name = surveyData.name ?? surveyData.initiatives?.[0]?.name ?? '-';

  const handleReload = async () => surveyDetails.refetch();

  const handleDownloadSurvey = async () => {
    return G17Client.downloadManagerSurveyStats(initiativeId, currentUser._id, surveyId);
  };

  return (
    <div className='admin-dashboard'>
      <Dashboard>
        <DashboardRow>
          <AdminBreadcrumbs
            initiativeId={initiativeId}
            breadcrumbs={[
              {
                label: 'Admin Dashboard',
                url: generateUrl(ROUTES.ADMIN_DASHBOARD, { initiativeId }),
              },
              {
                label: name,
              },
            ]}
          />
        </DashboardRow>

        <DashboardRow>
          <div className='pl-2 d-flex flex-column gap-2'>
            <TitleRow label={`${SURVEY.CAPITALIZED_SINGULAR} title:`}>
              <div
                className='d-flex align-items-center gap-4 cursor-pointer title-line'
                onClick={() => {
                  history.push(
                    generateUrl(ROUTES.COMPANY_TRACKER_SURVEY, {
                      initiativeId: surveyData.initiativeId,
                      surveyId,
                    })
                  );
                }}
              >
                <span className='fs-5'>{name}</span>
                <span className='text-ThemeTextLight'>{getFormattedSurveyDate(surveyData)}</span>
              </div>
            </TitleRow>
            <TitleRow label='Subsidiary:'>
              <div
                className='d-flex align-items-center gap-4 cursor-pointer title-line'
                onClick={() => {
                  history.push(generateUrl(ROUTES.ADMIN_DASHBOARD, { initiativeId: surveyData.initiativeId }));
                }}
              >
                <span className='fs-5'>{surveyData.initiatives?.[0]?.name}</span>
                <span className='text-ThemeTextLight'>
                  {getBranchInitiativeNameText({
                    initiativeTree: defaultTree,
                    initiativeId: surveyData.initiativeId,
                    showInitiativeId: rootInitiative?._id !== surveyData.initiativeId,
                  })}
                </span>
              </div>
            </TitleRow>
          </div>
        </DashboardRow>

        <div className='pr-3 d-flex justify-content-between align-items-center'>
          <ProgressRow title={name} progress={toPercentageProgress(progressStats)} />
          <div className='action-buttons text-nowrap d-flex align-items-center'>
            <CompleteButton
              key='complete'
              survey={surveyData}
              isUserAdmin={isUserAdmin}
              handleReload={handleReload}
              classes={{ btn: 'ml-2' }}
            />
          </div>
        </div>

        <DashboardSectionTitle title={QUESTION.CAPITALIZED_PLURAL} />

        <LoadingPlaceholder height={77} isLoading={!surveyGroups} className='mb-4'>
          <DashboardSection>
            {surveyUsers.isError ? <QueryError error={surveyUsers.error} /> : null}
            <QuestionManagementContainer>
              <div className='mt-3'>
                <SurveyQuestionList
                  surveyId={surveyData._id}
                  initiativeId={surveyData.initiativeId}
                  surveyGroups={surveyGroups}
                  disabledUTRs={disabledUTRs}
                  bulkActionToolbar={(props) => (
                    <BulkActionToolbar
                      {...props}
                      rootInitiativeUtrMap={rootInitiativeUtrMap}
                      surveyData={surveyData}
                      handleReload={handleReload}
                      hiddenOptions={getAdminRouteHiddenOptions(isRootOrganization)}
                    />
                  )}
                  columns={
                    delegationView
                      ? [
                          ColumnBookmark,
                          ColumnTitle,
                          Divider,
                          (props) => <ColumnContributors {...props} users={users} />,
                          (props) => <ColumnVerifiers {...props} users={users} />,
                          Divider,
                          ColumnStatusIcon,
                          ColumnOptionsMenu,
                        ]
                      : [
                          ColumnBookmark,
                          ColumnTitle,
                          Divider,
                          (props) => <ColumnValue {...props} unitConfig={surveyData?.unitConfig} />,
                          Divider,
                          ColumnStatusIcon,
                          ColumnOptionsMenu,
                        ]
                  }
                  toolbar={(props) => (
                    <SurveyQuestionListToolbar
                      {...props}
                      settings={filters}
                      handleChangeSettings={changeFilter}
                      surveyScope={surveyData.scope}
                      components={{
                        top: [
                          (props) => (
                            <DelegateActionsToolbar
                              setDelegationView={setDelegationView}
                              delegationView={delegationView}
                              handleDownloadSurvey={handleDownloadSurvey}
                              setShowMessageModal={setShowMessageModal}
                              {...props}
                            />
                          ),
                          SearchAndScopeToolbar,
                        ],
                        bottom: [FilterToggle, ExpandAllToggle],
                      }}
                    />
                  )}
                />
              </div>
            </QuestionManagementContainer>
          </DashboardSection>
        </LoadingPlaceholder>
      </Dashboard>
      {showMessageModal ? (
        <SurveyMessageModal surveyId={surveyId} toggle={() => setShowMessageModal(!showMessageModal)} />
      ) : null}
    </div>
  );
};
