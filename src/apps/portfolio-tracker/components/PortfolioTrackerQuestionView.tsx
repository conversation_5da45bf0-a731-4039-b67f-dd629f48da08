/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { standards } from '@g17eco/core';
import { useHistory } from 'react-router-dom';
import { Button } from 'reactstrap';
import Dashboard, { DashboardSection } from '@g17eco/molecules/dashboard';
import { NavigationButtons } from '@components/question/NavigationButtons';
import { NotApplicableBadge } from '@g17eco/molecules/question-status/NotApplicableBadge';
import { QuestionInput } from '@components/question/QuestionInput';
import { ROUTES } from '@constants/routes';
import { UtrvStatus } from '@constants/status';
import { useQuestionIds } from '@hooks/useQuestionIds';
import { getQuestionId } from '../../../selectors/survey';
import { ScopeQuestionGroup } from '@g17eco/types/survey';
import { nl2br } from '@utils/index';
import { generateUrl } from '@routes/util';
import { QUESTION } from '@constants/terminology';
import { CollapseButton, CollapseContent, CollapsePanel } from '@g17eco/molecules/collapse-panel';
import { RichTextEditorContainer } from '@features/rich-text-editor';
import { Comment } from '../../../components/utr-confirmation-modal/Comment';
import { ValueHistory } from '../../../types/universalTrackerValue';
import { getDisplayCheckboxAndOriginalValueData } from '@components/survey/question/questionUtil';
import { BasicAlert } from '@g17eco/molecules/alert';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { StatusBadge } from '@g17eco/molecules/question-status/StatusBadge';
import { UnitConfig } from '@models/surveyData';

interface PortfolioTrackerQuestionViewProps {
  portfolioId: string;
  companyId: string;
  surveyGroups: ScopeQuestionGroup[];
  questionId: string;
  index?: string;
  unitConfig?: UnitConfig;
}

export const PortfolioTrackerQuestionView = (props: PortfolioTrackerQuestionViewProps) => {
  const { surveyGroups, questionId, index, portfolioId, companyId, unitConfig } = props;
  const history = useHistory();
  const { questionListIds, utr, utrv, altCode } = useQuestionIds(surveyGroups, index, questionId);

  const goToQuestion = ({ id, index }: { id: any, index?: number }) => {
    const url = generateUrl(ROUTES.PORTFOLIO_TRACKER_SURVEY_OVERVIEW, {
      portfolioId,
      companyId,
      questionId: id,
      index: String(index ?? ''),
    })
    history.push(url);
  };

  const goNext = () => goToQuestion(getQuestionId(questionListIds, questionId, index ?? '', true));
  const goPrevious = () => goToQuestion(getQuestionId(questionListIds, questionId, index ?? '', false));

  const onBackClick = () => history.push(generateUrl(ROUTES.PORTFOLIO_TRACKER_SURVEY_OVERVIEW, { portfolioId, companyId }));
  const backButton = (
    <Button color='link' size='sm' onClick={onBackClick}>
      <i className='fa fa-arrow-circle-left mr-2' />
      Back to {QUESTION.CAPITALIZED_SINGULAR} List
    </Button>
  );
  const prevNextButton = <NavigationButtons goNext={goNext} goPrevious={goPrevious} isDisabled={questionListIds.length <= 1} />;

  if (!utr || !utrv) {
    return (
      <Dashboard className='assuranceContainer mt-2'>
        <DashboardSection title={backButton} buttons={[prevNextButton]}>
          <h5>{QUESTION.CAPITALIZED_SINGULAR} not available</h5>
        </DashboardSection>
      </Dashboard>
    )
  }

  const standardName = standards[altCode]?.name || '';
  const standardCode = utr.getTypeCode(altCode);

  const { displayCheckbox } = getDisplayCheckboxAndOriginalValueData({ valueType: utr.getValueType(), utrv });

  const isVerified = utrv.status === UtrvStatus.Verified;

  const instructionsText = `View ${standards[altCode]?.shortName || ''} Instructions`;
  const instructionsLink = utr.getInstructionsLink(altCode);

  const hasComments = [utrv.notes?.stakeholder, utrv.notes?.verifier].some(h => h?.note);

  return (
    <Dashboard className='assurance-question-view mt-0'>
      <DashboardSection title={backButton} padding={3} buttons={[prevNextButton]}>
        <div className='mb-3 d-relative text-ThemeTextPlaceholder'>
          <div className='d-flex align-items-center justify-content-between'>
            <div>{`${standardName} - ${standardCode}`}</div>
            <div className='d-flex align-items-center'>
              {isVerified ? <StatusBadge utrv={utrv} className='ml-2' /> : null}
              <NotApplicableBadge utrv={utrv} showExplanation={false} className='ml-2' />
            </div>
          </div>
          <div className='d-flex align-items-center mt-4 justify-content-end text-right'>
            {instructionsLink && (
              <Button color='link' size='sm' onClick={() => window.open(instructionsLink, '_blank')}>
                <u>{instructionsText}</u>
              </Button>
            )}
          </div>
        </div>
        <div className='mt-3 pt-2 strong text-xl'>{utr.getValueLabel(altCode)}</div>
        <div className='mt-3'>{nl2br(utr.getInstructions(altCode))}</div>
        <div className='mt-4'>
          <QuestionInput
            utrv={utrv}
            utr={utr}
            isReadOnly={true}
            displayCheckbox={displayCheckbox}
            unitConfig={unitConfig}
          />
        </div>
        {
          <CollapsePanel className='comment-group mt-3 py-3 border-top' collapsed>
            <CollapseButton classes={{ content: 'pb-1' }}>
              <h5 className='mb-0'>Further explanation / notes</h5>
            </CollapseButton>
            <CollapseContent>
              {!hasComments ? (
                <BasicAlert type='info' className='mt-4'>
                  There are no notes associated with this {QUESTION.SINGULAR}
                </BasicAlert>
              ) : (
                <RichTextEditorContainer>
                  <LoadingPlaceholder height={85} isLoading={!utr}>
                    {[utrv.notes?.stakeholder, utrv.notes?.verifier].map((history, index) => {
                      return history?.note ? (
                        <Comment
                          key={index ? 'verifier' : 'stakeholder'}
                          history={history as Pick<ValueHistory, 'action' | 'note'>}
                          handleComments={() => {}}
                          canEdit={false}
                          canUseRichTextEditor={false}
                        />
                      ) : null;
                    })}
                  </LoadingPlaceholder>
                </RichTextEditorContainer>
              )}
            </CollapseContent>
          </CollapsePanel>
        }
      </DashboardSection>
    </Dashboard>
  );
}
