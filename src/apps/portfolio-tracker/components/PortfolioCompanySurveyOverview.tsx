/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import React, { useCallback, useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import { useGetDataShareSurveyQuery } from '@api/g17ecoApi';
import { ROUTES } from '@constants/routes';
import { useAppSelector } from '../../../reducers';
import { ReduxStateLoadable } from '@reducers/types';
import { PortfolioTrackerQuestionView } from './PortfolioTrackerQuestionView';
import { generateUrl } from '@routes/util';
import G17Client from '@services/G17Client';
import { DataShareLookup, RequesterType } from '@g17eco/types/dataShare';
import { SurveyListItem } from '@g17eco/types/survey';
import { useSurveyGroups } from '@utils/survey';
import Dashboard, { DashboardSection } from '@g17eco/molecules/dashboard';
import { SurveyListDropdown } from '@components/initiative/SurveyListDropdown';
import { Loader } from '@g17eco/atoms/loader';
import SurveyQuestionList from '@components/survey-question-list/survey-question-list';
import { SurveyQuestionListToolbar } from '@components/survey/survey-question-list-toolbar';
import { ExpandAllToggle } from '@components/survey/survey-question-list-toolbar/partials/ExpandAllToggle';
import { ScopeViewDropdown } from '@components/survey/survey-question-list-toolbar/partials/ScopeViewDropdown';
import { NotShared } from './NotShared';
import { SURVEY } from '@constants/terminology';
import { BasicAlert } from '@g17eco/molecules/alert';
import { ColumnBookmark } from '@components/survey-question-list/partials/ColumnBookmark';
import { ColumnTitle } from '@g17eco/molecules/survey-question-list';
import { Divider } from '@g17eco/molecules/tracking-list';
import { ColumnValue } from '@components/survey-question-list/partials/ColumnValue';
import { ColumnStatusIcon } from '@components/survey-question-list/partials/ColumnStatusIcon';

interface RouteParams {
  questionId?: string;
  index?: string;
}

interface DataShareSurveysOverviewProps {
  initiativeId: string;
  portfolioId: string;
}

export const PortfolioCompanySurveyOverview = (props: DataShareSurveysOverviewProps) => {
  const { portfolioId, initiativeId } = props;
  const [item, setItem] = useState<SurveyListItem | undefined>();
  const [dataShare, setDataShare] = useState<ReduxStateLoadable<DataShareLookup>>({
    loaded: false,
    data: undefined,
    errored: false,
  });
  const history = useHistory();
  const { questionId, index } = useParams<RouteParams>();
  const surveyData = useGetDataShareSurveyQuery(
    {
      initiativeId,
      requesterId: portfolioId,
      requesterType: RequesterType.Portfolio,
      surveyId: item?._id || '',
    },
    { skip: !item?._id }
  );
  const overviewMode = useAppSelector((state) => state.surveySettings.overviewMode);
  const surveyGroups = useSurveyGroups(overviewMode, surveyData.data?.surveyData);

  useEffect(() => {
    G17Client.getRequesterDataShare({ requesterId: portfolioId, requesterType: RequesterType.Portfolio, initiativeId })
      .then((data) => {
        setDataShare((s) => ({ ...s, loaded: true, data }));
        setItem(data.list[0]);
      })
      .catch((e: Error) => {
        setDataShare((s) => ({
          ...s,
          loaded: false,
          data: undefined,
          errored: true,
          errorMessage: e.message,
        }));
      });
  }, [initiativeId, portfolioId]);

  const goToQuestion = useCallback(
    (id: string, index?: number) => {
      const url = generateUrl(ROUTES.PORTFOLIO_TRACKER_SURVEY_OVERVIEW, {
        portfolioId,
        companyId: initiativeId,
        questionId: id,
        index: String(index ?? ''),
      });
      history.push(url);
    },
    [history, initiativeId, portfolioId]
  );

  if (questionId) {
    return (
      <PortfolioTrackerQuestionView
        portfolioId={portfolioId}
        companyId={initiativeId}
        questionId={questionId}
        surveyGroups={surveyGroups}
        index={index}
        unitConfig={surveyData.data?.surveyData.unitConfig}
      />
    );
  }

  if (dataShare.errorMessage) {
    return (
      <Dashboard className='mt-0'>
        <DashboardSection>
          <BasicAlert type={'danger'}>{dataShare.errorMessage}</BasicAlert>
        </DashboardSection>
      </Dashboard>
    );
  }

  if (!dataShare.loaded || surveyData.isLoading || surveyData.isFetching) {
    return (
      <Dashboard className='mt-0'>
        <Loader relative={true} />
      </Dashboard>
    );
  }

  if (!item) {
    return (
      <NotShared title={SURVEY.CAPITALIZED_PLURAL}>
        <span>Company does not have any completed {SURVEY.PLURAL}</span>
      </NotShared>
    );
  }

  return (
    <Dashboard className='mt-0'>
      <DashboardSection buttons={[
        <SurveyListDropdown
          key='survey-list-dropdown'
          surveyList={dataShare.data.list}
          selectedItem={item}
          handleDropdownSelect={(item) => setItem(item)}
        />
      ]}>
        <SurveyQuestionList
          goToQuestion={goToQuestion}
          toolbar={(props) => (
            <SurveyQuestionListToolbar
              {...props}
              surveyScope={surveyData.data?.surveyData?.scope}
              components={{
                top: [ScopeViewDropdown],
                bottom: surveyGroups.length > 1 ? [ExpandAllToggle] : [],
              }}
            />
          )}
          surveyId={item._id}
          initiativeId={initiativeId}
          surveyGroups={surveyGroups}
          columns={[
            ColumnBookmark,
            ColumnTitle,
            Divider,
            (props) => <ColumnValue {...props} unitConfig={surveyData.data?.surveyData?.unitConfig} />,
            Divider,
            ColumnStatusIcon,
          ]}
        />
      </DashboardSection>
    </Dashboard>
  );
};
