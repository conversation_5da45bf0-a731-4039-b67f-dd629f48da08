import { companyTrackerCardConfig } from '@constants/app-card';
import { Button } from 'reactstrap';
import config from '../../../../config';
import { IntegrationCardVariant } from './types';

interface Props {
  variant: IntegrationCardVariant;
  toggle: () => void;
}

const baseClass = {
  container: 'd-flex justify-content-between align-items-center gap-2 h-100',
  buttonContainer: 'd-flex justify-content-between gap-2',
}

const CTLogo = `${config.media.logosBaseUrl}/company_tracker_logo.svg`;

const ColumnIntegrationCard = ({ toggle }: Omit<Props, 'variant'>) => {
  return (
    <div className={`${baseClass.container} flex-column`}>
      <img src={companyTrackerCardConfig.logo} alt='logo' style={{ maxHeight: '53px' }} />
      <div className={baseClass.buttonContainer}>
        <Button color='link-secondary' onClick={() => window.open(config.brochureURL.companyTracker, '_blank')}>
          More info
        </Button>
        <Button color='primary' onClick={toggle}>
          Start free trial
        </Button>
      </div>
    </div>
  );
};

const RowIntegrationCard = ({ toggle }: Omit<Props, 'variant'>) => {
  return (
    <div className={`${baseClass.container} flex-column flex-md-row`}>
      <img src={CTLogo} alt='logo' style={{ maxHeight: '40px' }} />
      <div>
        <div className='h5 text-ThemeAccentExtradark m-0'>Connect</div>
        <div className='h5 text-ThemeAccentExtradark m-0 fw-bold'>Company Tracker</div>
      </div>
      <div className={`${baseClass.buttonContainer} flex-column flex-shrink-0`}>
        <Button color='primary' onClick={toggle} size='xs'>
          Start free trial
        </Button>
        <Button color='link-secondary' size='xs' onClick={() => window.open(config.brochureURL.companyTracker, '_blank')}>
          More info
        </Button>
      </div>
    </div>
  );
};

export const IntegrationCard = (props: Props) => {
  switch (props.variant) {
    case IntegrationCardVariant.Column:
      return <ColumnIntegrationCard {...props} />;
    case IntegrationCardVariant.Row:
      return <RowIntegrationCard {...props} />;
    default:
      return null;
  }
};
